<libraries>
  <library
      name="com.android.support:design:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\8270adb3dcd9272142798ba9958ab579\transformed\design-28.0.0\jars\classes.jar"
      resolved="com.android.support:design:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\8270adb3dcd9272142798ba9958ab579\transformed\design-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:appcompat-v7:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\4973f91cd2444a4abd2089723a7e5dc9\transformed\appcompat-v7-28.0.0\jars\classes.jar"
      resolved="com.android.support:appcompat-v7:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\4973f91cd2444a4abd2089723a7e5dc9\transformed\appcompat-v7-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:cardview-v7:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\a7298b63126ca22b56a14d4e307079ac\transformed\cardview-v7-28.0.0\jars\classes.jar"
      resolved="com.android.support:cardview-v7:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\a7298b63126ca22b56a14d4e307079ac\transformed\cardview-v7-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-fragment:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\ebd18f75b9a3b0d73784dd6a5cbc30ef\transformed\support-fragment-28.0.0\jars\classes.jar"
      resolved="com.android.support:support-fragment:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\ebd18f75b9a3b0d73784dd6a5cbc30ef\transformed\support-fragment-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:animated-vector-drawable:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\a3fae80cda322ff85344e8c99f2ac9c5\transformed\animated-vector-drawable-28.0.0\jars\classes.jar"
      resolved="com.android.support:animated-vector-drawable:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\a3fae80cda322ff85344e8c99f2ac9c5\transformed\animated-vector-drawable-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:recyclerview-v7:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\75fa7e361cf1fa871ae34561b272908c\transformed\recyclerview-v7-28.0.0\jars\classes.jar"
      resolved="com.android.support:recyclerview-v7:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\75fa7e361cf1fa871ae34561b272908c\transformed\recyclerview-v7-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-core-ui:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\820ed1c3d2f0c9380d113a103fb22ade\transformed\support-core-ui-28.0.0\jars\classes.jar"
      resolved="com.android.support:support-core-ui:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\820ed1c3d2f0c9380d113a103fb22ade\transformed\support-core-ui-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-core-utils:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\bac4bf448ecaf23f82c8969bbd354931\transformed\support-core-utils-28.0.0\jars\classes.jar"
      resolved="com.android.support:support-core-utils:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\bac4bf448ecaf23f82c8969bbd354931\transformed\support-core-utils-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-vector-drawable:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\9a8fb577233d540612a6dcc52cc13314\transformed\support-vector-drawable-28.0.0\jars\classes.jar"
      resolved="com.android.support:support-vector-drawable:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\9a8fb577233d540612a6dcc52cc13314\transformed\support-vector-drawable-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:transition:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\fa87da10e9530b3689a72358af980e44\transformed\transition-28.0.0\jars\classes.jar"
      resolved="com.android.support:transition:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\fa87da10e9530b3689a72358af980e44\transformed\transition-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:loader:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\8c24645226d7261301ca11338088ba59\transformed\loader-28.0.0\jars\classes.jar"
      resolved="com.android.support:loader:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\8c24645226d7261301ca11338088ba59\transformed\loader-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:viewpager:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\7caf09b9e32c19af8bc0db15aa107d83\transformed\viewpager-28.0.0\jars\classes.jar"
      resolved="com.android.support:viewpager:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\7caf09b9e32c19af8bc0db15aa107d83\transformed\viewpager-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:coordinatorlayout:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\f29d80ca58f1f9b594bfd54574416d70\transformed\coordinatorlayout-28.0.0\jars\classes.jar"
      resolved="com.android.support:coordinatorlayout:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\f29d80ca58f1f9b594bfd54574416d70\transformed\coordinatorlayout-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:drawerlayout:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\8a0002004a593a9099acf9a4068e806c\transformed\drawerlayout-28.0.0\jars\classes.jar"
      resolved="com.android.support:drawerlayout:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\8a0002004a593a9099acf9a4068e806c\transformed\drawerlayout-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:slidingpanelayout:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\12278b8824a007d7c0d94cd6b99512f2\transformed\slidingpanelayout-28.0.0\jars\classes.jar"
      resolved="com.android.support:slidingpanelayout:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\12278b8824a007d7c0d94cd6b99512f2\transformed\slidingpanelayout-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:customview:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\3b419f68d713d52fa704e9f28db1c3dd\transformed\customview-28.0.0\jars\classes.jar"
      resolved="com.android.support:customview:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\3b419f68d713d52fa704e9f28db1c3dd\transformed\customview-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:swiperefreshlayout:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\62212203febe87e0c44738abb5569c8e\transformed\swiperefreshlayout-28.0.0\jars\classes.jar"
      resolved="com.android.support:swiperefreshlayout:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\62212203febe87e0c44738abb5569c8e\transformed\swiperefreshlayout-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:asynclayoutinflater:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\a18a0e7a8c82281bb74b0acfa9b21599\transformed\asynclayoutinflater-28.0.0\jars\classes.jar"
      resolved="com.android.support:asynclayoutinflater:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\a18a0e7a8c82281bb74b0acfa9b21599\transformed\asynclayoutinflater-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-compat:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\b401c224dd8ca0c8f00c38b06cf5788f\transformed\support-compat-28.0.0\jars\classes.jar"
      resolved="com.android.support:support-compat:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\b401c224dd8ca0c8f00c38b06cf5788f\transformed\support-compat-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:versionedparcelable:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\fe7de84392061693ae5f87ceab83d73e\transformed\versionedparcelable-28.0.0\jars\classes.jar"
      resolved="com.android.support:versionedparcelable:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\fe7de84392061693ae5f87ceab83d73e\transformed\versionedparcelable-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:collections:28.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.android.support\collections\28.0.0\c1bcdade4d3cc2836130424a3f3e4182c666a745\collections-28.0.0.jar"
      resolved="com.android.support:collections:28.0.0"/>
  <library
      name="com.android.support:cursoradapter:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\2a1c9bf0f34df4985b0f86076fd5dd9d\transformed\cursoradapter-28.0.0\jars\classes.jar"
      resolved="com.android.support:cursoradapter:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\2a1c9bf0f34df4985b0f86076fd5dd9d\transformed\cursoradapter-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="android.arch.lifecycle:runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\e847ff002a6ae0edde05d2c7b23526aa\transformed\runtime-1.1.1\jars\classes.jar"
      resolved="android.arch.lifecycle:runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\e847ff002a6ae0edde05d2c7b23526aa\transformed\runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:documentfile:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\18cff65c999935eb0190699b9d932c33\transformed\documentfile-28.0.0\jars\classes.jar"
      resolved="com.android.support:documentfile:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\18cff65c999935eb0190699b9d932c33\transformed\documentfile-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:localbroadcastmanager:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\2f07c6b3f0e237453a4acc37fbe8e722\transformed\localbroadcastmanager-28.0.0\jars\classes.jar"
      resolved="com.android.support:localbroadcastmanager:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\2f07c6b3f0e237453a4acc37fbe8e722\transformed\localbroadcastmanager-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:print:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\197abb9eb58a8e0d01372035003c92a6\transformed\print-28.0.0\jars\classes.jar"
      resolved="com.android.support:print:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\197abb9eb58a8e0d01372035003c92a6\transformed\print-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="android.arch.lifecycle:viewmodel:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\d1c14ed9d12cf6fc5db788b3e0a12ae2\transformed\viewmodel-1.1.1\jars\classes.jar"
      resolved="android.arch.lifecycle:viewmodel:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\d1c14ed9d12cf6fc5db788b3e0a12ae2\transformed\viewmodel-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:interpolator:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\dc50f2ed5a6bc63bd61830e2792249ea\transformed\interpolator-28.0.0\jars\classes.jar"
      resolved="com.android.support:interpolator:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\dc50f2ed5a6bc63bd61830e2792249ea\transformed\interpolator-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="android.arch.lifecycle:livedata:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\68b0bddc40886219e188fd5250bc759f\transformed\livedata-1.1.1\jars\classes.jar"
      resolved="android.arch.lifecycle:livedata:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\68b0bddc40886219e188fd5250bc759f\transformed\livedata-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="android.arch.lifecycle:livedata-core:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\fc12cf07d6e73a7f6ec580cb767a6220\transformed\livedata-core-1.1.1\jars\classes.jar"
      resolved="android.arch.lifecycle:livedata-core:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\fc12cf07d6e73a7f6ec580cb767a6220\transformed\livedata-core-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="android.arch.lifecycle:common:1.1.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\android.arch.lifecycle\common\1.1.1\207a6efae6a3555e326de41f76bdadd9a239cbce\common-1.1.1.jar"
      resolved="android.arch.lifecycle:common:1.1.1"/>
  <library
      name="android.arch.core:runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\ca327b899be1ce97a1642226f3df8380\transformed\runtime-1.1.1\jars\classes.jar"
      resolved="android.arch.core:runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\ca327b899be1ce97a1642226f3df8380\transformed\runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="android.arch.core:common:1.1.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\android.arch.core\common\1.1.1\e55b70d1f5620db124b3e85a7f4bdc7bd48d9f95\common-1.1.1.jar"
      resolved="android.arch.core:common:1.1.1"/>
  <library
      name="com.android.support:support-annotations:28.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.android.support\support-annotations\28.0.0\ed73f5337a002d1fd24339d5fb08c2c9d9ca60d8\support-annotations-28.0.0.jar"
      resolved="com.android.support:support-annotations:28.0.0"/>
  <library
      name="com.android.support.constraint:constraint-layout:1.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\868eef2cd99d9b153fbf31a49138b958\transformed\constraint-layout-1.1.3\jars\classes.jar"
      resolved="com.android.support.constraint:constraint-layout:1.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\868eef2cd99d9b153fbf31a49138b958\transformed\constraint-layout-1.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support.constraint:constraint-layout-solver:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.android.support.constraint\constraint-layout-solver\1.1.3\bde0667d7414c16ed62d3cfe993cff7f9d732373\constraint-layout-solver-1.1.3.jar"
      resolved="com.android.support.constraint:constraint-layout-solver:1.1.3"/>
  <library
      name="com.android.support:multidex:1.0.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.9\transforms\bbd9ec28dd32ea78e0900a1ea7d9bc6c\transformed\multidex-1.0.3\jars\classes.jar"
      resolved="com.android.support:multidex:1.0.3"
      folder="C:\Users\<USER>\.gradle\caches\8.9\transforms\bbd9ec28dd32ea78e0900a1ea7d9bc6c\transformed\multidex-1.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
</libraries>
