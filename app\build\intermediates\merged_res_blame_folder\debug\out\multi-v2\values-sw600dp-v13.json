{"logs": [{"outputFile": "com.example.bottomnavigationview.app-mergeDebugResources-10:/values-sw600dp-v13/values-sw600dp-v13.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\8270adb3dcd9272142798ba9958ab579\\transformed\\design-28.0.0\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,185,256,328,386,444,553,617,680", "endLines": "2,3,4,5,6,7,9,10,11,15", "endColumns": "59,69,70,71,57,57,10,63,62,10", "endOffsets": "110,180,251,323,381,439,548,612,675,847"}, "to": {"startLines": "10,11,12,13,14,15,16,18,19,20", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "611,671,741,812,884,942,1000,1109,1173,1236", "endLines": "10,11,12,13,14,15,17,18,19,23", "endColumns": "59,69,70,71,57,57,10,63,62,10", "endOffsets": "666,736,807,879,937,995,1104,1168,1231,1403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\4973f91cd2444a4abd2089723a7e5dc9\\transformed\\appcompat-v7-28.0.0\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,124,193,263,337,413,472,543", "endColumns": "68,68,69,73,75,58,70,67", "endOffsets": "119,188,258,332,408,467,538,606"}}]}]}