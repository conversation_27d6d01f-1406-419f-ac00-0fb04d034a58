package com.example.bottomnavigationview;

import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

public class HomeFragment extends Fragment {

    Button BtBuy,BtPre,BtCook,BtPrice;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_home,container,false);


    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        BtBuy=(Button)getActivity().findViewById(R.id.BtBuy);
        BtPre=(Button)getActivity().findViewById(R.id.BtPre);
        BtCook=(Button)getActivity().findViewById(R.id.BtCook);
        BtPrice=(Button)getActivity().findViewById(R.id.BtPice);


    BtBuy.setOnClickListener(new View.OnClickListener() {
    @Override
    public void onClick(View v) {
        Intent it=new Intent(getActivity(),BuyActivity.class);
        startActivity(it);
    }
});


        BtPre.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent it=new Intent(getActivity(),PreVegetables.class);
                startActivity(it);
            }
        });

        BtPrice.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent it=new Intent(getActivity(),BuyMoney.class);
                startActivity(it);
            }
        });

        BtCook.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent it=new Intent(getActivity(),VGanalysis.class);
                startActivity(it);
            }
        });
    }
}
