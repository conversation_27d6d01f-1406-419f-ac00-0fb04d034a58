<?xml version="1.0" encoding="utf-8"?>






    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="fill_parent"
        android:layout_height="?android:attr/listPreferredItemHeight">
        <ImageView android:id="@+id/photo"
            android:layout_width="80dip"
            android:layout_height="20dip"
            android:layout_alignParentTop="true"
            android:layout_alignParentBottom="true"
            android:adjustViewBounds="true"
            android:padding="2dip" />

        <TextView
            android:id="@+id/name1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_toRightOf="@+id/photo"
            android:ellipsize="marquee"
            android:singleLine="true"
            android:textSize="20dip" />
    </RelativeLayout>



