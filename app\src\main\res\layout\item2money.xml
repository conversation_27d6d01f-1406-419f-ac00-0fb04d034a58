<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="5dp">

    <LinearLayout
        android:layout_width="50dp"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/buyID"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="TextView"
            android:textAppearance="@style/TextAppearance.AppCompat.Display1"
            android:textColor="@color/colorAccent"
            tools:text="ID" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="2"
        android:background="@color/colorPrimaryDark"
        android:gravity="center_horizontal|center_vertical"
        android:orientation="vertical">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/textView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="種類：" />

            <TextView
                android:id="@+id/kind"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="TextView"
                tools:text="name" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/textView3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="數量：" />

            <TextView
                android:id="@+id/finekind"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="TextView"
                android:textColor="@color/common_google_signin_btn_text_dark_focused"
                tools:text="amount" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/textView5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="購買地點：" />

            <TextView
                android:id="@+id/part"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="TextView"
                tools:text="wherebuy" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/textView4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="購買日期：" />

            <TextView
                android:id="@+id/buydate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="TextView"
                android:textColor="@color/common_google_signin_btn_text_dark_focused"
                tools:text="buydate" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="184dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="horizontal">


        <TextView
            android:id="@+id/savedate"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="0.5"
            android:gravity="center"
            android:textColor="@color/colorAccent"
            android:textSize="18sp"
            tools:text="money" />

        <TextView
            android:id="@+id/textView6"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="元"
            android:textColor="@color/colorAccent"
            android:textSize="24sp" />

    </LinearLayout>

</LinearLayout>
