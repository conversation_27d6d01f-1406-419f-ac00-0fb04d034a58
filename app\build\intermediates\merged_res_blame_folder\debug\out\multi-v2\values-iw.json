{"logs": [{"outputFile": "com.example.bottomnavigationview.app-mergeDebugResources-10:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b401c224dd8ca0c8f00c38b06cf5788f\\transformed\\support-compat-28.0.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2832", "endColumns": "100", "endOffsets": "2928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\4973f91cd2444a4abd2089723a7e5dc9\\transformed\\appcompat-v7-28.0.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,308,416,500,600,714,792,870,961,1055,1151,1245,1346,1439,1534,1631,1722,1814,1895,1997,2101,2199,2302,2403,2503,2655,2751", "endColumns": "103,98,107,83,99,113,77,77,90,93,95,93,100,92,94,96,90,91,80,101,103,97,102,100,99,151,95,80", "endOffsets": "204,303,411,495,595,709,787,865,956,1050,1146,1240,1341,1434,1529,1626,1717,1809,1890,1992,2096,2194,2297,2398,2498,2650,2746,2827"}}]}]}