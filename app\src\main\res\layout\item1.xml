<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="150dp"
    android:layout_margin="5dp">

    <LinearLayout
        android:layout_width="40dp"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/buyID"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center|center_vertical|fill_vertical"
            android:text="TextView"
            android:textAppearance="@style/TextAppearance.AppCompat.Display1"
            android:textColor="@color/colorAccent"
            tools:text="ID" />
    </LinearLayout>

    <ImageView
        android:id="@+id/photo"
        android:layout_width="300dp"
        android:layout_height="150dp"
        android:layout_gravity="center|center_horizontal|center_vertical|end"
        android:layout_weight="1"
        android:scaleType="fitStart"
        app:srcCompat="@drawable/takepic" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="2.5"
        android:background="@color/colorPrimary"
        android:gravity="center_horizontal|center_vertical"
        android:orientation="vertical">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/textView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="種類：" />

            <TextView
                android:id="@+id/kind"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="TextView"
                tools:text="kind" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/textView3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="子類：" />

            <TextView
                android:id="@+id/finekind"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="TextView"
                android:textColor="@android:color/black"
                tools:text="finekind" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/textView5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="部位：" />

            <TextView
                android:id="@+id/part"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="TextView"
                tools:text="part" />
        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="184dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/textView4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="購買日期：" />

        </LinearLayout>

        <TextView
            android:id="@+id/buydate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="TextView"
            android:textColor="@android:color/black"
            tools:text="buydate" />

        <TextView
            android:id="@+id/savedate"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            tools:text="savedate" />

    </LinearLayout>

</LinearLayout>
