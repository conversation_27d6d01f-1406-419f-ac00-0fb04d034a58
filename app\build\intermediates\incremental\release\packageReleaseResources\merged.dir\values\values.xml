<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="array">
        <item name="Batman"/>
        <item name="Superman"/>
        <item name="Flash"/>
        <item name="<PERSON>"/>
    </string-array>
    <string-array name="items">
        <item>肉類</item>
        <item>海鮮類</item>
        <item>蔬果類</item>
        <item>奶蛋類</item>
        <item>麵食</item>
        <item>調味類</item>
        <item>熟食</item>
        <item>其它</item>
    </string-array>
    <string-array name="items3">
        <item>1份</item>
        <item>2份</item>
        <item>3份</item>
        <item>4份</item>
    </string-array>
    <string-array name="place">
        <item>冷藏</item>
        <item>冷凍</item>
        <item>蔬果層</item>
        <item>門側</item>
        <item>其它</item>


    </string-array>
    <string-array name="weekday">
        <item>星期日</item>
        <item>星期一</item>
        <item>星期二</item>
        <item>星期三</item>
        <item>星期四</item>
        <item>星期五</item>
        <item>星期六</item>
    </string-array>
    <color name="colorAccent">#FF4081</color>
    <color name="colorGreen">#247b37</color>
    <color name="colorGreen1">#3eb959</color>
    <color name="colorGreenLight">#abffc0</color>
    <color name="colorPrimary">#848cb7</color>
    <color name="colorPrimaryDark">#6d749f</color>
    <color name="colorRed">#ff3943</color>
    <color name="colorRedDark">#801b20</color>
    <color name="colorWhite">#ffffff</color>
    <string name="app_name">MyVG0809</string>
    <string name="negative_popup_text_defaulte">You have failed to complete the challenge. So close! Wanna have another go at it?</string>
    <string name="negative_popup_title_defaulte">Awww.... Snap!</string>
    <string name="positive_popup_text_default">You have successfully completed the challenge. Keep it up to earn more points.</string>
    <string name="positive_popup_title_default">Like a boss!</string>
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>
</resources>