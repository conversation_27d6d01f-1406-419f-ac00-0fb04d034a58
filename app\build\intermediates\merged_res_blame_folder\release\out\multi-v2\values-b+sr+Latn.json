{"logs": [{"outputFile": "com.example.bottomnavigationview.app-mergeReleaseResources-10:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b401c224dd8ca0c8f00c38b06cf5788f\\transformed\\support-compat-28.0.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2912", "endColumns": "100", "endOffsets": "3008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\4973f91cd2444a4abd2089723a7e5dc9\\transformed\\appcompat-v7-28.0.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,318,424,510,614,732,814,895,986,1079,1175,1269,1370,1463,1558,1663,1754,1845,1933,2039,2147,2248,2353,2461,2562,2731,2828", "endColumns": "108,103,105,85,103,117,81,80,90,92,95,93,100,92,94,104,90,90,87,105,107,100,104,107,100,168,96,83", "endOffsets": "209,313,419,505,609,727,809,890,981,1074,1170,1264,1365,1458,1553,1658,1749,1840,1928,2034,2142,2243,2348,2456,2557,2726,2823,2907"}}]}]}