[{"merged": "com.example.bottomnavigationview.app-merged_res-12:/layout_activity_buymoney.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/layout/activity_buymoney.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.example.bottomnavigationview.app-main-14:/mipmap-hdpi/ic_launcher.png"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable-v24_ic_launcher_foreground.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable-v24/ic_launcher_foreground.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/layout_item.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/layout/item.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "com.example.bottomnavigationview.app-main-14:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/layout_activity_vganalysis.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/layout/activity_vganalysis.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/mipmap-xxxhdpi_ic_launcher_round.png.flat", "source": "com.example.bottomnavigationview.app-main-14:/mipmap-xxxhdpi/ic_launcher_round.png"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_meat.jpg.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/meat.jpg"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/mipmap-hdpi_welcom2.png.flat", "source": "com.example.bottomnavigationview.app-main-14:/mipmap-hdpi/welcom2.png"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_seafood.jpg.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/seafood.jpg"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/layout_activity_main.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/layout/activity_main.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/menu_bottom_navigation.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/menu/bottom_navigation.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/mipmap-xxhdpi_ic_launcher_round.png.flat", "source": "com.example.bottomnavigationview.app-main-14:/mipmap-xxhdpi/ic_launcher_round.png"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_vege1.jpg.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/vege1.jpg"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_welcom.jpg.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/welcom.jpg"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "com.example.bottomnavigationview.app-main-14:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/layout_disc2.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/layout/disc2.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_ic_home_black_24dp.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/ic_home_black_24dp.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/layout_fragment_search.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/layout/fragment_search.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/layout_item2money.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/layout/item2money.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_ic_search_black_24dp.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/ic_search_black_24dp.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_ic_local_bar_black_24dp.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/ic_local_bar_black_24dp.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/layout_fragment_home.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/layout/fragment_home.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/layout_fireforulist2.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/layout/fireforulist2.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/mipmap-mdpi_ic_launcher_round.png.flat", "source": "com.example.bottomnavigationview.app-main-14:/mipmap-mdpi/ic_launcher_round.png"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_ic_close_black_24dp.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/ic_close_black_24dp.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/mipmap-xhdpi_ic_launcher.png.flat", "source": "com.example.bottomnavigationview.app-main-14:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_ic_favorite_black_24dp.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/ic_favorite_black_24dp.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/layout_activity_welcome.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/layout/activity_welcome.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/layout_activity_buy.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/layout/activity_buy.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_ic_launch_black_24dp.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/ic_launch_black_24dp.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_cookbear2.jpg.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/cookbear2.jpg"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/layout_activity_recipe.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/layout/activity_recipe.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_ic_error_outline_black_24dp.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/ic_error_outline_black_24dp.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_vegetable.jpg.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/vegetable.jpg"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/layout_epic_popup_negtive.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/layout/epic_popup_negtive.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/layout_forum_listitem2.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/layout/forum_listitem2.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_button_red_round.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/button_red_round.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/mipmap-xhdpi_ic_launcher_round.png.flat", "source": "com.example.bottomnavigationview.app-main-14:/mipmap-xhdpi/ic_launcher_round.png"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_speech_bubble.png.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/speech_bubble.png"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_bill.jpg.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/bill.jpg"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_outside.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/outside.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/mipmap-mdpi_ic_launcher.png.flat", "source": "com.example.bottomnavigationview.app-main-14:/mipmap-mdpi/ic_launcher.png"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/layout_fragment_favorites.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/layout/fragment_favorites.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_ic_launcher_background.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/layout_epic_popup_positive.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/layout/epic_popup_positive.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/layout_item1.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/layout/item1.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_cookbear.jpg.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/cookbear.jpg"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/layout_activity_pre_vegetables.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/layout/activity_pre_vegetables.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_takepic.png.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/takepic.png"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/mipmap-hdpi_ic_launcher_round.png.flat", "source": "com.example.bottomnavigationview.app-main-14:/mipmap-hdpi/ic_launcher_round.png"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/layout_disc_content2.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/layout/disc_content2.xml"}, {"merged": "com.example.bottomnavigationview.app-merged_res-12:/drawable_button_green_round.xml.flat", "source": "com.example.bottomnavigationview.app-main-14:/drawable/button_green_round.xml"}]