{"logs": [{"outputFile": "com.example.bottomnavigationview.app-mergeDebugResources-10:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b401c224dd8ca0c8f00c38b06cf5788f\\transformed\\support-compat-28.0.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2817", "endColumns": "100", "endOffsets": "2913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\4973f91cd2444a4abd2089723a7e5dc9\\transformed\\appcompat-v7-28.0.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,412,498,601,714,792,870,961,1054,1147,1241,1342,1435,1530,1624,1715,1805,1884,1984,2084,2180,2283,2382,2489,2642,2738", "endColumns": "101,98,105,85,102,112,77,77,90,92,92,93,100,92,94,93,90,89,78,99,99,95,102,98,106,152,95,78", "endOffsets": "202,301,407,493,596,709,787,865,956,1049,1142,1236,1337,1430,1525,1619,1710,1800,1879,1979,2079,2175,2278,2377,2484,2637,2733,2812"}}]}]}