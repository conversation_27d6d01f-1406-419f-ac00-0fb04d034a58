{"logs": [{"outputFile": "com.example.bottomnavigationview.app-mergeReleaseResources-10:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\4973f91cd2444a4abd2089723a7e5dc9\\transformed\\appcompat-v7-28.0.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,809,888,979,1072,1167,1261,1360,1453,1548,1642,1733,1824,1904,2016,2125,2222,2331,2434,2541,2700,2801", "endColumns": "110,104,107,86,103,110,77,78,90,92,94,93,98,92,94,93,90,90,79,111,108,96,108,102,106,158,100,79", "endOffsets": "211,316,424,511,615,726,804,883,974,1067,1162,1256,1355,1448,1543,1637,1728,1819,1899,2011,2120,2217,2326,2429,2536,2695,2796,2876"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b401c224dd8ca0c8f00c38b06cf5788f\\transformed\\support-compat-28.0.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2881", "endColumns": "100", "endOffsets": "2977"}}]}]}