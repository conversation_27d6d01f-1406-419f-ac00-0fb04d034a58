<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.bottomnavigationview"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="34" />

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="android.support.v4.app.CoreComponentFactory"
        android:extractNativeLibs="true"
        android:icon="@mipmap/welcom2"
        android:label="Grace"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme" >
        <activity
            android:name="com.example.bottomnavigationview.Recipe"
            android:exported="false" />
        <activity
            android:name="com.example.bottomnavigationview.WelcomeActivty"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.example.bottomnavigationview.MainActivity"
            android:exported="false" />
        <activity
            android:name="com.example.bottomnavigationview.BuyActivity"
            android:exported="false" />
        <activity
            android:name="com.example.bottomnavigationview.PreVegetables"
            android:exported="false" />
        <activity
            android:name="com.example.bottomnavigationview.BuyMoney"
            android:exported="false" />
        <activity
            android:name="com.example.bottomnavigationview.FireForumListActivity2"
            android:exported="false" />
        <activity
            android:name="com.example.bottomnavigationview.FireDiscActivity2"
            android:exported="false" />
        <activity
            android:name="com.example.bottomnavigationview.VGanalysis"
            android:exported="false" />
    </application>

</manifest>