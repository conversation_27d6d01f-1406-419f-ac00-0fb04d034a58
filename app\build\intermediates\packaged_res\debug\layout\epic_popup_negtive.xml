<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginLeft="5dp"
    android:layout_marginRight="5dp"
    android:gravity="center">


    <ImageView
        android:id="@+id/closePopupNegativeImg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginTop="7dp"
        android:layout_marginRight="7dp"
        android:contentDescription="TODO"
        android:elevation="5dp"
        app:srcCompat="@drawable/ic_close_black_24dp" />

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="15dp"
        app:cardBackgroundColor="@color/colorRed"
        android:layout_alignParentTop="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="25dp"
                android:layout_marginBottom="25dp">

                <TextView
                    android:id="@+id/titleTv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:textAlignment="center"
                    android:textSize="25sp"
                    android:textColor="@color/colorWhite"
                    android:textStyle="bold"
                    android:text="@string/negative_popup_title_defaulte"/>

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_marginBottom="10dp"
                    app:srcCompat="@drawable/ic_error_outline_black_24dp"
                    android:contentDescription="TODO"></ImageView>

                <TextView
                    android:id="@+id/messageTv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_marginBottom="20dp"
                    android:textAlignment="center"
                    android:textSize="18sp"
                    android:textColor="@color/colorWhite"
                    android:text="@string/negative_popup_text_defaulte"></TextView>



                <Button
                    android:id="@+id/btnRetry"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:text="Try Again"
                    android:textColor="@color/colorAccent"
                    android:background="@drawable/button_red_round"
                    android:layout_gravity="center_horizontal"/>









            </LinearLayout>


        </LinearLayout>







    </androidx.cardview.widget.CardView>


</RelativeLayout>