<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginLeft="5dp"
    android:layout_marginRight="5dp"
    android:gravity="center">

    <ImageView
        android:id="@+id/closePopupPositiveImg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:layout_marginRight="7dp"
        android:elevation="5dp"
        app:srcCompat="@drawable/ic_close_black_24dp"
        android:layout_alignParentRight="true"
        android:contentDescription="TODO"
        android:layout_marginEnd="7dp" />

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="15dp"
        app:cardBackgroundColor="@color/colorGreenLight"
        android:layout_alignParentTop="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="25dp"
                android:layout_marginBottom="25dp">

                <TextView
                    android:id="@+id/titleTv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:textAlignment="center"
                    android:textSize="25dp"
                    android:textStyle="bold"
                    android:text="@string/positive_popup_title_default"/>

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_marginBottom="10dp"
                    app:srcCompat="@drawable/ic_local_bar_black_24dp"></ImageView>

                <TextView
                    android:id="@+id/messageTv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_marginBottom="20dp"
                    android:textAlignment="center"
                    android:textSize="18dp"
                    android:text="@string/positive_popup_text_default"></TextView>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_marginBottom="10dp"
                    android:textAlignment="center"
                    android:textSize="20dp"
                    android:textStyle="bold"
                    android:text="+57 points"></TextView>

                <Button
                    android:id="@+id/btnAccept"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:text="YESSSSSS"
                    android:textColor="@color/colorAccent"
                    android:background="@drawable/button_green_round"
                    android:layout_gravity="center_horizontal"/>









            </LinearLayout>


        </LinearLayout>







    </androidx.cardview.widget.CardView>



</RelativeLayout>