{"logs": [{"outputFile": "com.example.bottomnavigationview.app-mergeReleaseResources-10:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\4973f91cd2444a4abd2089723a7e5dc9\\transformed\\appcompat-v7-28.0.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,312,423,509,617,735,816,897,988,1081,1180,1274,1375,1468,1563,1661,1752,1843,1927,2032,2141,2240,2346,2457,2566,2732,2830", "endColumns": "106,99,110,85,107,117,80,80,90,92,98,93,100,92,94,97,90,90,83,104,108,98,105,110,108,165,97,87", "endOffsets": "207,307,418,504,612,730,811,892,983,1076,1175,1269,1370,1463,1558,1656,1747,1838,1922,2027,2136,2235,2341,2452,2561,2727,2825,2913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b401c224dd8ca0c8f00c38b06cf5788f\\transformed\\support-compat-28.0.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2918", "endColumns": "100", "endOffsets": "3014"}}]}]}