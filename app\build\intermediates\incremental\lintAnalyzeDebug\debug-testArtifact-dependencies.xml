<dependencies>
  <compile
      roots="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)@@:app::debug,junit:junit:4.13.2@jar,org.hamcrest:hamcrest-core:1.3@jar,com.android.support:design:28.0.0@aar,com.android.support:appcompat-v7:28.0.0@aar,com.android.support:cardview-v7:28.0.0@aar,com.android.support:support-fragment:28.0.0@aar,com.android.support:animated-vector-drawable:28.0.0@aar,com.android.support:recyclerview-v7:28.0.0@aar,com.android.support:support-core-ui:28.0.0@aar,com.android.support:support-core-utils:28.0.0@aar,com.android.support:support-vector-drawable:28.0.0@aar,com.android.support:transition:28.0.0@aar,com.android.support:loader:28.0.0@aar,com.android.support:viewpager:28.0.0@aar,com.android.support:coordinatorlayout:28.0.0@aar,com.android.support:drawerlayout:28.0.0@aar,com.android.support:slidingpanelayout:28.0.0@aar,com.android.support:customview:28.0.0@aar,com.android.support:swiperefreshlayout:28.0.0@aar,com.android.support:asynclayoutinflater:28.0.0@aar,com.android.support:support-compat:28.0.0@aar,com.android.support:versionedparcelable:28.0.0@aar,com.android.support:collections:28.0.0@jar,com.android.support:cursoradapter:28.0.0@aar,android.arch.lifecycle:runtime:1.1.1@aar,com.android.support:documentfile:28.0.0@aar,com.android.support:localbroadcastmanager:28.0.0@aar,com.android.support:print:28.0.0@aar,android.arch.lifecycle:viewmodel:1.1.1@aar,com.android.support:interpolator:28.0.0@aar,android.arch.lifecycle:livedata:1.1.1@aar,android.arch.lifecycle:livedata-core:1.1.1@aar,android.arch.lifecycle:common:1.1.1@jar,android.arch.core:runtime:1.1.1@aar,android.arch.core:common:1.1.1@jar,com.android.support:support-annotations:28.0.0@jar,com.android.support.constraint:constraint-layout:1.1.3@aar,com.android.support.constraint:constraint-layout-solver:1.1.3@jar,com.android.support:multidex:1.0.3@aar">
    <dependency
        name="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)@@:app::debug"
        simpleName=":"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="com.android.support:design:28.0.0@aar"
        simpleName="com.android.support:design"/>
    <dependency
        name="com.android.support:appcompat-v7:28.0.0@aar"
        simpleName="com.android.support:appcompat-v7"/>
    <dependency
        name="com.android.support:cardview-v7:28.0.0@aar"
        simpleName="com.android.support:cardview-v7"/>
    <dependency
        name="com.android.support:support-fragment:28.0.0@aar"
        simpleName="com.android.support:support-fragment"/>
    <dependency
        name="com.android.support:animated-vector-drawable:28.0.0@aar"
        simpleName="com.android.support:animated-vector-drawable"/>
    <dependency
        name="com.android.support:recyclerview-v7:28.0.0@aar"
        simpleName="com.android.support:recyclerview-v7"/>
    <dependency
        name="com.android.support:support-core-ui:28.0.0@aar"
        simpleName="com.android.support:support-core-ui"/>
    <dependency
        name="com.android.support:support-core-utils:28.0.0@aar"
        simpleName="com.android.support:support-core-utils"/>
    <dependency
        name="com.android.support:support-vector-drawable:28.0.0@aar"
        simpleName="com.android.support:support-vector-drawable"/>
    <dependency
        name="com.android.support:transition:28.0.0@aar"
        simpleName="com.android.support:transition"/>
    <dependency
        name="com.android.support:loader:28.0.0@aar"
        simpleName="com.android.support:loader"/>
    <dependency
        name="com.android.support:viewpager:28.0.0@aar"
        simpleName="com.android.support:viewpager"/>
    <dependency
        name="com.android.support:coordinatorlayout:28.0.0@aar"
        simpleName="com.android.support:coordinatorlayout"/>
    <dependency
        name="com.android.support:drawerlayout:28.0.0@aar"
        simpleName="com.android.support:drawerlayout"/>
    <dependency
        name="com.android.support:slidingpanelayout:28.0.0@aar"
        simpleName="com.android.support:slidingpanelayout"/>
    <dependency
        name="com.android.support:customview:28.0.0@aar"
        simpleName="com.android.support:customview"/>
    <dependency
        name="com.android.support:swiperefreshlayout:28.0.0@aar"
        simpleName="com.android.support:swiperefreshlayout"/>
    <dependency
        name="com.android.support:asynclayoutinflater:28.0.0@aar"
        simpleName="com.android.support:asynclayoutinflater"/>
    <dependency
        name="com.android.support:support-compat:28.0.0@aar"
        simpleName="com.android.support:support-compat"/>
    <dependency
        name="com.android.support:versionedparcelable:28.0.0@aar"
        simpleName="com.android.support:versionedparcelable"/>
    <dependency
        name="com.android.support:collections:28.0.0@jar"
        simpleName="com.android.support:collections"/>
    <dependency
        name="com.android.support:cursoradapter:28.0.0@aar"
        simpleName="com.android.support:cursoradapter"/>
    <dependency
        name="android.arch.lifecycle:runtime:1.1.1@aar"
        simpleName="android.arch.lifecycle:runtime"/>
    <dependency
        name="com.android.support:documentfile:28.0.0@aar"
        simpleName="com.android.support:documentfile"/>
    <dependency
        name="com.android.support:localbroadcastmanager:28.0.0@aar"
        simpleName="com.android.support:localbroadcastmanager"/>
    <dependency
        name="com.android.support:print:28.0.0@aar"
        simpleName="com.android.support:print"/>
    <dependency
        name="android.arch.lifecycle:viewmodel:1.1.1@aar"
        simpleName="android.arch.lifecycle:viewmodel"/>
    <dependency
        name="com.android.support:interpolator:28.0.0@aar"
        simpleName="com.android.support:interpolator"/>
    <dependency
        name="android.arch.lifecycle:livedata:1.1.1@aar"
        simpleName="android.arch.lifecycle:livedata"/>
    <dependency
        name="android.arch.lifecycle:livedata-core:1.1.1@aar"
        simpleName="android.arch.lifecycle:livedata-core"/>
    <dependency
        name="android.arch.lifecycle:common:1.1.1@jar"
        simpleName="android.arch.lifecycle:common"/>
    <dependency
        name="android.arch.core:runtime:1.1.1@aar"
        simpleName="android.arch.core:runtime"/>
    <dependency
        name="android.arch.core:common:1.1.1@jar"
        simpleName="android.arch.core:common"/>
    <dependency
        name="com.android.support:support-annotations:28.0.0@jar"
        simpleName="com.android.support:support-annotations"/>
    <dependency
        name="com.android.support.constraint:constraint-layout:1.1.3@aar"
        simpleName="com.android.support.constraint:constraint-layout"/>
    <dependency
        name="com.android.support.constraint:constraint-layout-solver:1.1.3@jar"
        simpleName="com.android.support.constraint:constraint-layout-solver"/>
    <dependency
        name="com.android.support:multidex:1.0.3@aar"
        simpleName="com.android.support:multidex"/>
  </compile>
  <package
      roots="junit:junit:4.13.2@jar,com.android.support:design:28.0.0@aar,com.android.support:appcompat-v7:28.0.0@aar,com.android.support.constraint:constraint-layout:1.1.3@aar,com.android.support:multidex:1.0.3@aar,com.android.support:cardview-v7:28.0.0@aar,org.hamcrest:hamcrest-core:1.3@jar,com.android.support:support-fragment:28.0.0@aar,com.android.support:animated-vector-drawable:28.0.0@aar,com.android.support:recyclerview-v7:28.0.0@aar,com.android.support:support-core-ui:28.0.0@aar,com.android.support:support-core-utils:28.0.0@aar,com.android.support:support-vector-drawable:28.0.0@aar,com.android.support:transition:28.0.0@aar,com.android.support:loader:28.0.0@aar,com.android.support:viewpager:28.0.0@aar,com.android.support:coordinatorlayout:28.0.0@aar,com.android.support:drawerlayout:28.0.0@aar,com.android.support:slidingpanelayout:28.0.0@aar,com.android.support:customview:28.0.0@aar,com.android.support:swiperefreshlayout:28.0.0@aar,com.android.support:asynclayoutinflater:28.0.0@aar,com.android.support:support-compat:28.0.0@aar,com.android.support:versionedparcelable:28.0.0@aar,com.android.support:collections:28.0.0@jar,com.android.support:cursoradapter:28.0.0@aar,android.arch.lifecycle:runtime:1.1.1@aar,com.android.support:documentfile:28.0.0@aar,com.android.support:localbroadcastmanager:28.0.0@aar,com.android.support:print:28.0.0@aar,android.arch.lifecycle:viewmodel:1.1.1@aar,com.android.support:interpolator:28.0.0@aar,android.arch.lifecycle:livedata:1.1.1@aar,android.arch.lifecycle:livedata-core:1.1.1@aar,android.arch.lifecycle:common:1.1.1@jar,android.arch.core:runtime:1.1.1@aar,android.arch.core:common:1.1.1@jar,com.android.support:support-annotations:28.0.0@jar,com.android.support.constraint:constraint-layout-solver:1.1.3@jar">
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="com.android.support:design:28.0.0@aar"
        simpleName="com.android.support:design"/>
    <dependency
        name="com.android.support:appcompat-v7:28.0.0@aar"
        simpleName="com.android.support:appcompat-v7"/>
    <dependency
        name="com.android.support.constraint:constraint-layout:1.1.3@aar"
        simpleName="com.android.support.constraint:constraint-layout"/>
    <dependency
        name="com.android.support:multidex:1.0.3@aar"
        simpleName="com.android.support:multidex"/>
    <dependency
        name="com.android.support:cardview-v7:28.0.0@aar"
        simpleName="com.android.support:cardview-v7"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="com.android.support:support-fragment:28.0.0@aar"
        simpleName="com.android.support:support-fragment"/>
    <dependency
        name="com.android.support:animated-vector-drawable:28.0.0@aar"
        simpleName="com.android.support:animated-vector-drawable"/>
    <dependency
        name="com.android.support:recyclerview-v7:28.0.0@aar"
        simpleName="com.android.support:recyclerview-v7"/>
    <dependency
        name="com.android.support:support-core-ui:28.0.0@aar"
        simpleName="com.android.support:support-core-ui"/>
    <dependency
        name="com.android.support:support-core-utils:28.0.0@aar"
        simpleName="com.android.support:support-core-utils"/>
    <dependency
        name="com.android.support:support-vector-drawable:28.0.0@aar"
        simpleName="com.android.support:support-vector-drawable"/>
    <dependency
        name="com.android.support:transition:28.0.0@aar"
        simpleName="com.android.support:transition"/>
    <dependency
        name="com.android.support:loader:28.0.0@aar"
        simpleName="com.android.support:loader"/>
    <dependency
        name="com.android.support:viewpager:28.0.0@aar"
        simpleName="com.android.support:viewpager"/>
    <dependency
        name="com.android.support:coordinatorlayout:28.0.0@aar"
        simpleName="com.android.support:coordinatorlayout"/>
    <dependency
        name="com.android.support:drawerlayout:28.0.0@aar"
        simpleName="com.android.support:drawerlayout"/>
    <dependency
        name="com.android.support:slidingpanelayout:28.0.0@aar"
        simpleName="com.android.support:slidingpanelayout"/>
    <dependency
        name="com.android.support:customview:28.0.0@aar"
        simpleName="com.android.support:customview"/>
    <dependency
        name="com.android.support:swiperefreshlayout:28.0.0@aar"
        simpleName="com.android.support:swiperefreshlayout"/>
    <dependency
        name="com.android.support:asynclayoutinflater:28.0.0@aar"
        simpleName="com.android.support:asynclayoutinflater"/>
    <dependency
        name="com.android.support:support-compat:28.0.0@aar"
        simpleName="com.android.support:support-compat"/>
    <dependency
        name="com.android.support:versionedparcelable:28.0.0@aar"
        simpleName="com.android.support:versionedparcelable"/>
    <dependency
        name="com.android.support:collections:28.0.0@jar"
        simpleName="com.android.support:collections"/>
    <dependency
        name="com.android.support:cursoradapter:28.0.0@aar"
        simpleName="com.android.support:cursoradapter"/>
    <dependency
        name="android.arch.lifecycle:runtime:1.1.1@aar"
        simpleName="android.arch.lifecycle:runtime"/>
    <dependency
        name="com.android.support:documentfile:28.0.0@aar"
        simpleName="com.android.support:documentfile"/>
    <dependency
        name="com.android.support:localbroadcastmanager:28.0.0@aar"
        simpleName="com.android.support:localbroadcastmanager"/>
    <dependency
        name="com.android.support:print:28.0.0@aar"
        simpleName="com.android.support:print"/>
    <dependency
        name="android.arch.lifecycle:viewmodel:1.1.1@aar"
        simpleName="android.arch.lifecycle:viewmodel"/>
    <dependency
        name="com.android.support:interpolator:28.0.0@aar"
        simpleName="com.android.support:interpolator"/>
    <dependency
        name="android.arch.lifecycle:livedata:1.1.1@aar"
        simpleName="android.arch.lifecycle:livedata"/>
    <dependency
        name="android.arch.lifecycle:livedata-core:1.1.1@aar"
        simpleName="android.arch.lifecycle:livedata-core"/>
    <dependency
        name="android.arch.lifecycle:common:1.1.1@jar"
        simpleName="android.arch.lifecycle:common"/>
    <dependency
        name="android.arch.core:runtime:1.1.1@aar"
        simpleName="android.arch.core:runtime"/>
    <dependency
        name="android.arch.core:common:1.1.1@jar"
        simpleName="android.arch.core:common"/>
    <dependency
        name="com.android.support:support-annotations:28.0.0@jar"
        simpleName="com.android.support:support-annotations"/>
    <dependency
        name="com.android.support.constraint:constraint-layout-solver:1.1.3@jar"
        simpleName="com.android.support.constraint:constraint-layout-solver"/>
  </package>
</dependencies>
