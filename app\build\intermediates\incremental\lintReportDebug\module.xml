<lint-module
    format="1"
    dir="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app"
    name=":app"
    type="APP"
    maven="MyVG(20200201):app:"
    agpVersion="8.1.4"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-34\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\33.0.1\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="debug"/>
</lint-module>
