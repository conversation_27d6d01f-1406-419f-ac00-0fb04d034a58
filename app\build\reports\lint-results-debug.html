<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Lint Report</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
 <link rel="stylesheet" href="https://code.getmdl.io/1.2.1/material.blue-indigo.min.css" />
<link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Roboto:300,400,500,700" type="text/css">
<script defer src="https://code.getmdl.io/1.2.0/material.min.js"></script>
<style>
section.section--center {
    max-width: 860px;
}
.mdl-card__supporting-text + .mdl-card__actions {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
}
main > .mdl-layout__tab-panel {
  padding: 8px;
  padding-top: 48px;
}

.mdl-card__actions {
    margin: 0;
    padding: 4px 40px;
    color: inherit;
}
.mdl-card > * {
    height: auto;
}
.mdl-card__actions a {
    color: #00BCD4;
    margin: 0;
}
.error-icon {
    color: #bb7777;
    vertical-align: bottom;
}
.warning-icon {
    vertical-align: bottom;
}
.mdl-layout__content section:not(:last-of-type) {
  position: relative;
  margin-bottom: 48px;
}

.mdl-card .mdl-card__supporting-text {
  margin: 40px;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding: 0;
  color: inherit;
  width: calc(100% - 80px);
}
div.mdl-layout__drawer-button .material-icons {
    line-height: 48px;
}
.mdl-card .mdl-card__supporting-text {
    margin-top: 0px;
}
.chips {
    float: right;
    vertical-align: middle;
}

pre.errorlines {
    background-color: white;
    font-family: monospace;
    border: 1px solid #e0e0e0;
    line-height: 0.9rem;
    font-size: 0.9rem;    padding: 1px 0px 1px; 1px;
    overflow: scroll;
}
.prefix {
    color: #660e7a;
    font-weight: bold;
}
.attribute {
    color: #0000ff;
    font-weight: bold;
}
.value {
    color: #008000;
    font-weight: bold;
}
.tag {
    color: #000080;
    font-weight: bold;
}
.comment {
    color: #808080;
    font-style: italic;
}
.javadoc {
    color: #808080;
    font-style: italic;
}
.annotation {
    color: #808000;
}
.string {
    color: #008000;
    font-weight: bold;
}
.number {
    color: #0000ff;
}
.keyword {
    color: #000080;
    font-weight: bold;
}
.caretline {
    background-color: #fffae3;
}
.lineno {
    color: #999999;
    background-color: #f0f0f0;
}
.error {
    display: inline-block;
    position:relative;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AwCFR4T/3uLMgAAADxJREFUCNdNyLERQEAABMCjL4lQwIzcjErpguAL+C9AvgKJDbeD/PRpLdm35Hm+MU+cB+tCKaJW4L4YBy+CAiLJrFs9mgAAAABJRU5ErkJggg==) bottom repeat-x;
}
.warning {
    text-decoration: none;
    background-color: #f6ebbc;
}
.overview {
    padding: 10pt;
    width: 100%;
    overflow: auto;
    border-collapse:collapse;
}
.overview tr {
    border-bottom: solid 1px #eeeeee;
}
.categoryColumn a {
     text-decoration: none;
     color: inherit;
}
.countColumn {
    text-align: right;
    padding-right: 20px;
    width: 50px;
}
.issueColumn {
   padding-left: 16px;
}
.categoryColumn {
   position: relative;
   left: -50px;
   padding-top: 20px;
   padding-bottom: 5px;
}
.options {
   padding-left: 16px;
}
</style>
<script language="javascript" type="text/javascript">
<!--
function reveal(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'block';
document.getElementById(id+'Link').style.display = 'none';
}
}
function hideid(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'none';
}
}
//-->
</script>
</head>
<body class="mdl-color--grey-100 mdl-color-text--grey-700 mdl-base">
<div class="mdl-layout mdl-js-layout mdl-layout--fixed-header">
  <header class="mdl-layout__header">
    <div class="mdl-layout__header-row">
      <span class="mdl-layout-title">Lint Report: 26 errors and 227 warnings</span>
      <div class="mdl-layout-spacer"></div>
      <nav class="mdl-navigation mdl-layout--large-screen-only">Check performed at Fri Jun 20 10:31:56 CST 2025 by AGP (8.1.4)</nav>
    </div>
  </header>
  <div class="mdl-layout__drawer">
    <span class="mdl-layout-title">Issue Types</span>
    <nav class="mdl-navigation">
      <a class="mdl-navigation__link" href="#overview"><i class="material-icons">dashboard</i>Overview</a>
      <a class="mdl-navigation__link" href="#OnClick"><i class="material-icons error-icon">error</i><code>onClick</code> method does not exist (2)</a>
      <a class="mdl-navigation__link" href="#GradleCompatible"><i class="material-icons error-icon">error</i>Incompatible Gradle Versions (3)</a>
      <a class="mdl-navigation__link" href="#MissingClass"><i class="material-icons error-icon">error</i>Missing registered class (2)</a>
      <a class="mdl-navigation__link" href="#ScopedStorage"><i class="material-icons warning-icon">warning</i>Affected by scoped storage (1)</a>
      <a class="mdl-navigation__link" href="#ApplySharedPref"><i class="material-icons warning-icon">warning</i>Use <code>apply()</code> on <code>SharedPreferences</code> (1)</a>
      <a class="mdl-navigation__link" href="#OldTargetApi"><i class="material-icons warning-icon">warning</i>Target SDK attribute is not targeting latest version (1)</a>
      <a class="mdl-navigation__link" href="#Range"><i class="material-icons error-icon">error</i>Outside Range (19)</a>
      <a class="mdl-navigation__link" href="#ShowToast"><i class="material-icons warning-icon">warning</i>Toast created but not shown (2)</a>
      <a class="mdl-navigation__link" href="#SimpleDateFormat"><i class="material-icons warning-icon">warning</i>Implied locale in date format (2)</a>
      <a class="mdl-navigation__link" href="#InflateParams"><i class="material-icons warning-icon">warning</i>Layout Inflation without a Parent (2)</a>
      <a class="mdl-navigation__link" href="#GradleDependency"><i class="material-icons warning-icon">warning</i>Obsolete Gradle Dependency (1)</a>
      <a class="mdl-navigation__link" href="#SourceLockedOrientationActivity"><i class="material-icons warning-icon">warning</i>Incompatible setRequestedOrientation value (1)</a>
      <a class="mdl-navigation__link" href="#SpUsage"><i class="material-icons warning-icon">warning</i>Using <code>dp</code> instead of <code>sp</code> for text sizes (5)</a>
      <a class="mdl-navigation__link" href="#Typos"><i class="material-icons warning-icon">warning</i>Spelling error (1)</a>
      <a class="mdl-navigation__link" href="#Recycle"><i class="material-icons warning-icon">warning</i>Missing <code>recycle()</code> calls (3)</a>
      <a class="mdl-navigation__link" href="#ViewHolder"><i class="material-icons warning-icon">warning</i>View Holder Candidates (1)</a>
      <a class="mdl-navigation__link" href="#HandlerLeak"><i class="material-icons warning-icon">warning</i>Handler reference leaks (2)</a>
      <a class="mdl-navigation__link" href="#DisableBaselineAlignment"><i class="material-icons warning-icon">warning</i>Missing <code>baselineAligned</code> attribute (1)</a>
      <a class="mdl-navigation__link" href="#InefficientWeight"><i class="material-icons warning-icon">warning</i>Inefficient layout weight (6)</a>
      <a class="mdl-navigation__link" href="#NestedWeights"><i class="material-icons warning-icon">warning</i>Nested layout weights (3)</a>
      <a class="mdl-navigation__link" href="#Overdraw"><i class="material-icons warning-icon">warning</i>Overdraw: Painting regions more than once (8)</a>
      <a class="mdl-navigation__link" href="#UnusedResources"><i class="material-icons warning-icon">warning</i>Unused resources (8)</a>
      <a class="mdl-navigation__link" href="#UselessLeaf"><i class="material-icons warning-icon">warning</i>Unnecessary leaf layout (1)</a>
      <a class="mdl-navigation__link" href="#UselessParent"><i class="material-icons warning-icon">warning</i>Unnecessary parent layout (7)</a>
      <a class="mdl-navigation__link" href="#MonochromeLauncherIcon"><i class="material-icons warning-icon">warning</i>Monochrome icon is not defined (1)</a>
      <a class="mdl-navigation__link" href="#IconLocation"><i class="material-icons warning-icon">warning</i>Image defined in density-independent drawable folder (10)</a>
      <a class="mdl-navigation__link" href="#ButtonStyle"><i class="material-icons warning-icon">warning</i>Button should be borderless (6)</a>
      <a class="mdl-navigation__link" href="#Autofill"><i class="material-icons warning-icon">warning</i>Use Autofill (5)</a>
      <a class="mdl-navigation__link" href="#ContentDescription"><i class="material-icons warning-icon">warning</i>Image without <code>contentDescription</code> (18)</a>
      <a class="mdl-navigation__link" href="#LabelFor"><i class="material-icons warning-icon">warning</i>Missing accessibility label (4)</a>
      <a class="mdl-navigation__link" href="#SetTextI18n"><i class="material-icons warning-icon">warning</i>TextView Internationalization (6)</a>
      <a class="mdl-navigation__link" href="#HardcodedText"><i class="material-icons warning-icon">warning</i>Hardcoded text (84)</a>
      <a class="mdl-navigation__link" href="#RtlHardcoded"><i class="material-icons warning-icon">warning</i>Using left/right instead of start/end attributes (36)</a>
    </nav>
  </div>
  <main class="mdl-layout__content">
    <div class="mdl-layout__tab-panel is-active">
<a name="overview"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverviewCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overview</h2>
  </div>
              <div class="mdl-card__supporting-text">
<table class="overview">
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Correctness">Correctness</a>
</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#OnClick">OnClick</a>: <code>onClick</code> method does not exist</td></tr>
<tr>
<td class="countColumn">3</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#GradleCompatible">GradleCompatible</a>: Incompatible Gradle Versions</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#MissingClass">MissingClass</a>: Missing registered class</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ScopedStorage">ScopedStorage</a>: Affected by scoped storage</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ApplySharedPref">ApplySharedPref</a>: Use <code>apply()</code> on <code>SharedPreferences</code></td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#OldTargetApi">OldTargetApi</a>: Target SDK attribute is not targeting latest version</td></tr>
<tr>
<td class="countColumn">19</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#Range">Range</a>: Outside Range</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ShowToast">ShowToast</a>: Toast created but not shown</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#SimpleDateFormat">SimpleDateFormat</a>: Implied locale in date format</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#InflateParams">InflateParams</a>: Layout Inflation without a Parent</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#GradleDependency">GradleDependency</a>: Obsolete Gradle Dependency</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#SourceLockedOrientationActivity">SourceLockedOrientationActivity</a>: Incompatible setRequestedOrientation value</td></tr>
<tr>
<td class="countColumn">5</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#SpUsage">SpUsage</a>: Using <code>dp</code> instead of <code>sp</code> for text sizes</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Correctness:Messages">Correctness:Messages</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#Typos">Typos</a>: Spelling error</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Performance">Performance</a>
</td></tr>
<tr>
<td class="countColumn">3</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#Recycle">Recycle</a>: Missing <code>recycle()</code> calls</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ViewHolder">ViewHolder</a>: View Holder Candidates</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#HandlerLeak">HandlerLeak</a>: Handler reference leaks</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#DisableBaselineAlignment">DisableBaselineAlignment</a>: Missing <code>baselineAligned</code> attribute</td></tr>
<tr>
<td class="countColumn">6</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#InefficientWeight">InefficientWeight</a>: Inefficient layout weight</td></tr>
<tr>
<td class="countColumn">3</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#NestedWeights">NestedWeights</a>: Nested layout weights</td></tr>
<tr>
<td class="countColumn">8</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#Overdraw">Overdraw</a>: Overdraw: Painting regions more than once</td></tr>
<tr>
<td class="countColumn">8</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UnusedResources">UnusedResources</a>: Unused resources</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UselessLeaf">UselessLeaf</a>: Unnecessary leaf layout</td></tr>
<tr>
<td class="countColumn">7</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UselessParent">UselessParent</a>: Unnecessary parent layout</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Usability:Icons">Usability:Icons</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#MonochromeLauncherIcon">MonochromeLauncherIcon</a>: Monochrome icon is not defined</td></tr>
<tr>
<td class="countColumn">10</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#IconLocation">IconLocation</a>: Image defined in density-independent drawable folder</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Usability">Usability</a>
</td></tr>
<tr>
<td class="countColumn">6</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ButtonStyle">ButtonStyle</a>: Button should be borderless</td></tr>
<tr>
<td class="countColumn">5</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#Autofill">Autofill</a>: Use Autofill</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Accessibility">Accessibility</a>
</td></tr>
<tr>
<td class="countColumn">18</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ContentDescription">ContentDescription</a>: Image without <code>contentDescription</code></td></tr>
<tr>
<td class="countColumn">4</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#LabelFor">LabelFor</a>: Missing accessibility label</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Internationalization">Internationalization</a>
</td></tr>
<tr>
<td class="countColumn">6</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#SetTextI18n">SetTextI18n</a>: TextView Internationalization</td></tr>
<tr>
<td class="countColumn">84</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#HardcodedText">HardcodedText</a>: Hardcoded text</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Internationalization:Bidirectional Text">Internationalization:Bidirectional Text</a>
</td></tr>
<tr>
<td class="countColumn">36</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#RtlHardcoded">RtlHardcoded</a>: Using left/right instead of start/end attributes</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#MissingIssues">Disabled Checks (40)</a>
</td></tr>
</table>
<br/>              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverviewCardLink" onclick="hideid('OverviewCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Correctness"></a>
<a name="OnClick"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OnClickCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">onClick method does not exist</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/fragment_favorites.xml">../../src/main/res/layout/fragment_favorites.xml</a>:72</span>: <span class="message">Corresponding method handler '<code>public void enterForum(android.view.View)</code>' not found</span><br /><pre class="errorlines">
<span class="lineno"> 69 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"8dp"</span>
<span class="lineno"> 70 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"8dp"</span>
<span class="lineno"> 71 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 72 </span>        <span class="error"><span class="prefix">android:</span><span class="attribute">onClick</span>=<span class="value">"enterForum"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 73 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"食譜推薦區"</span>
<span class="lineno"> 74 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintEnd_toEndOf</span>=<span class="value">"@+id/nickname"</span>
<span class="lineno"> 75 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintTop_toBottomOf</span>=<span class="value">"@+id/nickname"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_home.xml">../../src/main/res/layout/fragment_home.xml</a>:24</span>: <span class="message">Corresponding method handler '<code>public void BtBuy(android.view.View)</code>' not found</span><br /><pre class="errorlines">
<span class="lineno">  21 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"8dp"</span>
<span class="lineno">  22 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"8dp"</span>
<span class="lineno">  23 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno">  24 </span>        <span class="error"><span class="prefix">android:</span><span class="attribute">onClick</span>=<span class="value">"BtBuy"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  25 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"買菜"</span>
<span class="lineno">  26 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"28sp"</span>
<span class="lineno">  27 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintBottom_toTopOf</span>=<span class="value">"@+id/guideline2"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationOnClick" style="display: none;">
The <code>onClick</code> attribute value should be the name of a method in this View's context to invoke when the view is clicked. This name must correspond to a public method that takes exactly one parameter of type <code>View</code>.<br/>
<br/>
Must be a string value, using '\;' to escape characters such as '\n' or '\uxxxx' for a unicode character.<br/>To suppress this error, use the issue id "OnClick" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">OnClick</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 10/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationOnClickLink" onclick="reveal('explanationOnClick');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OnClickCardLink" onclick="hideid('OnClickCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="GradleCompatible"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="GradleCompatibleCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Incompatible Gradle Versions</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle">../../build.gradle</a>:46</span>: <span class="message">Version 28 (intended for Android Pie and below) is the last version of the legacy support library, so we recommend that you migrate to AndroidX libraries when using Android Q and moving forward. The IDE can help with this: Refactor > Migrate to AndroidX...</span><br /><pre class="errorlines">
<span class="lineno"> 43 </span>    implementation fileTree(dir: <span class="string">'libs'</span>, include: [<span class="string">'*.jar'</span>])
<span class="lineno"> 44 </span>
<span class="lineno"> 45 </span>    <span class="comment">// Support Library (Legacy)
</span>
<span class="caretline"><span class="lineno"> 46 </span>    implementation <span class="error"><span class="string">'com.android.support:appcompat-v7:28.0.0'</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 47 </span>    implementation <span class="string">'com.android.support.constraint:constraint-layout:1.1.3'</span>
<span class="lineno"> 48 </span>    implementation <span class="string">'com.android.support:multidex:1.0.3'</span>
<span class="lineno"> 49 </span>    implementation <span class="string">'com.android.support:cardview-v7:28.0.0'</span>
</pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:49</span>: <span class="message">Version 28 (intended for Android Pie and below) is the last version of the legacy support library, so we recommend that you migrate to AndroidX libraries when using Android Q and moving forward. The IDE can help with this: Refactor > Migrate to AndroidX...</span><br /><pre class="errorlines">
<span class="lineno"> 46 </span>    implementation <span class="string">'com.android.support:appcompat-v7:28.0.0'</span>
<span class="lineno"> 47 </span>    implementation <span class="string">'com.android.support.constraint:constraint-layout:1.1.3'</span>
<span class="lineno"> 48 </span>    implementation <span class="string">'com.android.support:multidex:1.0.3'</span>
<span class="caretline"><span class="lineno"> 49 </span>    implementation <span class="error"><span class="string">'com.android.support:cardview-v7:28.0.0'</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 50 </span>    implementation <span class="string">'com.android.support:design:28.0.0'</span>
<span class="lineno"> 51 </span>
<span class="lineno"> 52 </span>    <span class="comment">// Firebase (Temporarily disabled for compatibility)
</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:50</span>: <span class="message">Version 28 (intended for Android Pie and below) is the last version of the legacy support library, so we recommend that you migrate to AndroidX libraries when using Android Q and moving forward. The IDE can help with this: Refactor > Migrate to AndroidX...</span><br /><pre class="errorlines">
<span class="lineno"> 47 </span>    implementation <span class="string">'com.android.support.constraint:constraint-layout:1.1.3'</span>
<span class="lineno"> 48 </span>    implementation <span class="string">'com.android.support:multidex:1.0.3'</span>
<span class="lineno"> 49 </span>    implementation <span class="string">'com.android.support:cardview-v7:28.0.0'</span>
<span class="caretline"><span class="lineno"> 50 </span>    implementation <span class="error"><span class="string">'com.android.support:design:28.0.0'</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 51 </span>
<span class="lineno"> 52 </span>    <span class="comment">// Firebase (Temporarily disabled for compatibility)
</span>
<span class="lineno"> 53 </span>    <span class="comment">// implementation 'com.google.firebase:firebase-analytics:17.6.0'
</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationGradleCompatible" style="display: none;">
There are some combinations of libraries, or tools and libraries, that are incompatible, or can lead to bugs. One such incompatibility is compiling with a version of the Android support libraries that is not the latest version (or in particular, a version lower than your <code>targetSdkVersion</code>).<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "GradleCompatible" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">GradleCompatible</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Fatal</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 8/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationGradleCompatibleLink" onclick="reveal('explanationGradleCompatible');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="GradleCompatibleCardLink" onclick="hideid('GradleCompatibleCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="MissingClass"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingClassCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Missing registered class</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/epic_popup_negtive.xml">../../src/main/res/layout/epic_popup_negtive.xml</a>:22</span>: <span class="message">Class referenced in the layout file, <code>androidx.cardview.widget.CardView</code>, was not found in the project or the libraries</span><br /><pre class="errorlines">
<span class="lineno">  19 </span>        <span class="prefix">android:</span><span class="attribute">elevation</span>=<span class="value">"5dp"</span>
<span class="lineno">  20 </span>        <span class="prefix">app:</span><span class="attribute">srcCompat</span>=<span class="value">"@drawable/ic_close_black_24dp"</span> />
<span class="lineno">  21 </span>
<span class="caretline"><span class="lineno">  22 </span>    <span class="error"><span class="tag">&lt;androidx.cardview.widget.CardView</span><span class="attribute">
</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  23 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  24 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  25 </span>        <span class="prefix">app:</span><span class="attribute">cardCornerRadius</span>=<span class="value">"15dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_positive.xml">../../src/main/res/layout/epic_popup_positive.xml</a>:22</span>: <span class="message">Class referenced in the layout file, <code>androidx.cardview.widget.CardView</code>, was not found in the project or the libraries</span><br /><pre class="errorlines">
<span class="lineno">  19 </span>        <span class="prefix">android:</span><span class="attribute">contentDescription</span>=<span class="value">"TODO"</span>
<span class="lineno">  20 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"7dp"</span> />
<span class="lineno">  21 </span>
<span class="caretline"><span class="lineno">  22 </span>    <span class="error"><span class="tag">&lt;androidx.cardview.widget.CardView</span><span class="attribute">
</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  23 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  24 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  25 </span>        <span class="prefix">app:</span><span class="attribute">cardCornerRadius</span>=<span class="value">"15dp"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationMissingClass" style="display: none;">
If a class is referenced in the manifest or in a layout file, it must also exist in the project (or in one of the libraries included by the project. This check helps uncover typos in registration names, or attempts to rename or move classes without updating the XML references properly.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/manifest-intro.html">https://developer.android.com/guide/topics/manifest/manifest-intro.html</a>
</div>To suppress this error, use the issue id "MissingClass" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">MissingClass</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 8/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationMissingClassLink" onclick="reveal('explanationMissingClass');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingClassCardLink" onclick="hideid('MissingClassCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ScopedStorage"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ScopedStorageCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Affected by scoped storage</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:4</span>: <span class="message">WRITE_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to write to shared storage, use the <code>MediaStore.createWriteRequest</code> intent.</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="lineno">  2 </span><span class="tag">&lt;manifest</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>>
<span class="lineno">  3 </span>
<span class="caretline"><span class="lineno">  4 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="warning"><span class="value">android.permission.WRITE_EXTERNAL_STORAGE</span></span><span class="value">"</span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  5 </span>
<span class="lineno">  6 </span>    <span class="tag">&lt;application</span><span class="attribute">
</span><span class="lineno">  7 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">allowBackup</span>=<span class="value">"true"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationScopedStorage" style="display: none;">
Scoped storage is enforced on Android 10+ (or Android 11+ if using <code>requestLegacyExternalStorage</code>). In particular, <code>WRITE_EXTERNAL_STORAGE</code> will no longer provide write access to all files; it will provide the equivalent of <code>READ_EXTERNAL_STORAGE</code> instead.<br/>
<br/>
As of Android 13, if you need to query or interact with MediaStore or media files on the shared storage, you should be using instead one or more new storage permissions:<br/>
* <code>android.permission.READ_MEDIA_IMAGES</code><br/>
* <code>android.permission.READ_MEDIA_VIDEO</code><br/>
* <code>android.permission.READ_MEDIA_AUDIO</code><br/>
<br/>
and then add <code>maxSdkVersion="33"</code> to the older permission. See the developer guide for how to do this: <a href="https://developer.android.com/about/versions/13/behavior-changes-13#granular-media-permissions">https://developer.android.com/about/versions/13/behavior-changes-13#granular-media-permissions</a><br/>
<br/>
The <code>MANAGE_EXTERNAL_STORAGE</code> permission can be used to manage all files, but it is rarely necessary and most apps on Google Play are not allowed to use it. Most apps should instead migrate to use scoped storage. To modify or delete files, apps should request write access from the user as described at <a href="https://goo.gle/android-mediastore-createwriterequest">https://goo.gle/android-mediastore-createwriterequest</a>.<br/>
<br/>
To learn more, read these resources: Play policy: <a href="https://goo.gle/policy-storage-help">https://goo.gle/policy-storage-help</a> Allowable use cases: <a href="https://goo.gle/policy-storage-usecases">https://goo.gle/policy-storage-usecases</a><br/><div class="moreinfo">More info: <a href="https://goo.gle/android-storage-usecases">https://goo.gle/android-storage-usecases</a>
</div>To suppress this error, use the issue id "ScopedStorage" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ScopedStorage</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 8/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationScopedStorageLink" onclick="reveal('explanationScopedStorage');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ScopedStorageCardLink" onclick="hideid('ScopedStorageCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ApplySharedPref"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ApplySharedPrefCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use apply() on SharedPreferences</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/FavoritesFragment.java">../../src/main/java/com/example/bottomnavigationview/FavoritesFragment.java</a>:104</span>: <span class="message">Consider using <code>apply()</code> instead; <code>commit</code> writes its data to persistent storage immediately, whereas <code>apply</code> will handle it in the background</span><br /><pre class="errorlines">
<span class="lineno"> 101 </span>  <span class="comment">// if (user != null) {</span>
<span class="lineno"> 102 </span>  <span class="keyword">if</span> (<span class="keyword">true</span>) { <span class="comment">// Temporarily allow access without Firebase</span>
<span class="lineno"> 103 </span>      <span class="comment">//進入下一個 Activity</span>
<span class="caretline"><span class="lineno"> 104 </span>      nameSetting.edit().putString(<span class="string">"name"</span>, nicknameStrig).<span class="warning">commit()</span>;<span class="comment">//getSharedPreferences()方法，呼叫edit()方法取得編輯器物件，</span></span>
<span class="lineno"> 105 </span>      <span class="comment">// 此時使用匿名方式呼叫Editor的putString()方法將nicknaeString字串的內容寫入設定檔，資料標籤為”name”。</span>
<span class="lineno"> 106 </span>      <span class="comment">// 最後必須呼叫commit()方法，此時資料才真正寫入到設定檔中。</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationApplySharedPref" style="display: none;">
Consider using <code>apply()</code> instead of <code>commit</code> on shared preferences. Whereas <code>commit</code> blocks and writes its data to persistent storage immediately, <code>apply</code> will handle it in the background.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ApplySharedPref" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ApplySharedPref</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationApplySharedPrefLink" onclick="reveal('explanationApplySharedPref');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ApplySharedPrefCardLink" onclick="hideid('ApplySharedPrefCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="OldTargetApi"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OldTargetApiCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Target SDK attribute is not targeting latest version</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle">../../build.gradle</a>:13</span>: <span class="message">Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details.</span><br /><pre class="errorlines">
<span class="lineno"> 10 </span>    defaultConfig {
<span class="lineno"> 11 </span>        applicationId <span class="string">"com.example.bottomnavigationview"</span>
<span class="lineno"> 12 </span>        minSdk <span class="number">21</span>
<span class="caretline"><span class="lineno"> 13 </span>        <span class="warning">targetSdk <span class="number">34</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span>        versionCode <span class="number">1</span>
<span class="lineno"> 15 </span>        versionName <span class="string">"1.0"</span>
<span class="lineno"> 16 </span>        testInstrumentationRunner <span class="string">"androidx.test.runner.AndroidJUnitRunner"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationOldTargetApi" style="display: none;">
When your application runs on a version of Android that is more recent than your <code>targetSdkVersion</code> specifies that it has been tested with, various compatibility modes kick in. This ensures that your application continues to work, but it may look out of place. For example, if the <code>targetSdkVersion</code> is less than 14, your app may get an option button in the UI.<br/>
<br/>
To fix this issue, set the <code>targetSdkVersion</code> to the highest available value. Then test your app to make sure everything works correctly. You may want to consult the compatibility notes to see what changes apply to each version you are adding support for: <a href="https://developer.android.com/reference/android/os/Build.VERSION_CODES.html">https://developer.android.com/reference/android/os/Build.VERSION_CODES.html</a> as well as follow this guide:<br/>
<a href="https://developer.android.com/distribute/best-practices/develop/target-sdk.html">https://developer.android.com/distribute/best-practices/develop/target-sdk.html</a><br/><div class="moreinfo">More info: <ul><li><a href="https://developer.android.com/distribute/best-practices/develop/target-sdk.html">https://developer.android.com/distribute/best-practices/develop/target-sdk.html</a>
<li><a href="https://developer.android.com/reference/android/os/Build.VERSION_CODES.html">https://developer.android.com/reference/android/os/Build.VERSION_CODES.html</a>
</ul></div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "OldTargetApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">OldTargetApi</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationOldTargetApiLink" onclick="reveal('explanationOldTargetApi');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OldTargetApiCardLink" onclick="hideid('OldTargetApiCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="Range"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RangeCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Outside Range</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/BuyMoney.java">../../src/main/java/com/example/bottomnavigationview/BuyMoney.java</a>:115</span>: <span class="message">Value must be &#8805; 0 but <code>getColumnIndex</code> can be -1</span><br /><pre class="errorlines">
<span class="lineno"> 112 </span>
<span class="lineno"> 113 </span>  <span class="keyword">do</span> {            <span class="comment">////~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~迴圈</span>
<span class="lineno"> 114 </span>
<span class="caretline"><span class="lineno"> 115 </span>      eName = cursor.getString(<span class="error">cursor.getColumnIndex(<span class="string">"name"</span>)</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 116 </span>      mListName[index++] = eName; <span class="comment">//臨時字串變數,放置集合用</span>
<span class="lineno"> 117 </span>
<span class="lineno"> 118 </span>      eAmount = cursor.getString(cursor.getColumnIndex(<span class="string">"amount"</span>));
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/BuyMoney.java">../../src/main/java/com/example/bottomnavigationview/BuyMoney.java</a>:118</span>: <span class="message">Value must be &#8805; 0 but <code>getColumnIndex</code> can be -1</span><br /><pre class="errorlines">
<span class="lineno"> 115 </span>            eName = cursor.getString(cursor.getColumnIndex(<span class="string">"name"</span>));
<span class="lineno"> 116 </span>            mListName[index++] = eName; <span class="comment">//臨時字串變數,放置集合用</span>
<span class="lineno"> 117 </span>
<span class="caretline"><span class="lineno"> 118 </span>            eAmount = cursor.getString(<span class="error">cursor.getColumnIndex(<span class="string">"amount"</span>)</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 119 </span>
<span class="lineno"> 120 </span>            ebuydate = cursor.getString(cursor.getColumnIndex(<span class="string">"buydate"</span>));
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/BuyMoney.java">../../src/main/java/com/example/bottomnavigationview/BuyMoney.java</a>:120</span>: <span class="message">Value must be &#8805; 0 but <code>getColumnIndex</code> can be -1</span><br /><pre class="errorlines">
<span class="lineno"> 117 </span>
<span class="lineno"> 118 </span>          eAmount = cursor.getString(cursor.getColumnIndex(<span class="string">"amount"</span>));
<span class="lineno"> 119 </span>
<span class="caretline"><span class="lineno"> 120 </span>          ebuydate = cursor.getString(<span class="error">cursor.getColumnIndex(<span class="string">"buydate"</span>)</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 121 </span>
<span class="lineno"> 122 </span>          ewherebuy = cursor.getString(cursor.getColumnIndex(<span class="string">"wherebuy"</span>));
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/BuyMoney.java">../../src/main/java/com/example/bottomnavigationview/BuyMoney.java</a>:122</span>: <span class="message">Value must be &#8805; 0 but <code>getColumnIndex</code> can be -1</span><br /><pre class="errorlines">
<span class="lineno"> 119 </span>
<span class="lineno"> 120 </span>          ebuydate = cursor.getString(cursor.getColumnIndex(<span class="string">"buydate"</span>));
<span class="lineno"> 121 </span>
<span class="caretline"><span class="lineno"> 122 </span>          ewherebuy = cursor.getString(<span class="error">cursor.getColumnIndex(<span class="string">"wherebuy"</span>)</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 123 </span>
<span class="lineno"> 124 </span>          eID = cursor.getString(cursor.getColumnIndex(<span class="string">"_id"</span>));
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/BuyMoney.java">../../src/main/java/com/example/bottomnavigationview/BuyMoney.java</a>:124</span>: <span class="message">Value must be &#8805; 0 but <code>getColumnIndex</code> can be -1</span><br /><pre class="errorlines">
<span class="lineno"> 121 </span>
<span class="lineno"> 122 </span>          ewherebuy = cursor.getString(cursor.getColumnIndex(<span class="string">"wherebuy"</span>));
<span class="lineno"> 123 </span>
<span class="caretline"><span class="lineno"> 124 </span>          eID = cursor.getString(<span class="error">cursor.getColumnIndex(<span class="string">"_id"</span>)</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 125 </span>
<span class="lineno"> 126 </span>          ebuyprice=cursor.getString(cursor.getColumnIndex(<span class="string">"buyprice"</span>));
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="RangeDivLink" onclick="reveal('RangeDiv');" />+ 14 More Occurrences...</button>
<div id="RangeDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/BuyMoney.java">../../src/main/java/com/example/bottomnavigationview/BuyMoney.java</a>:126</span>: <span class="message">Value must be &#8805; 0 but <code>getColumnIndex</code> can be -1</span><br /><pre class="errorlines">
<span class="lineno"> 123 </span>
<span class="lineno"> 124 </span>   eID = cursor.getString(cursor.getColumnIndex(<span class="string">"_id"</span>));
<span class="lineno"> 125 </span>
<span class="caretline"><span class="lineno"> 126 </span>   ebuyprice=cursor.getString(<span class="error">cursor.getColumnIndex(<span class="string">"buyprice"</span>)</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 127 </span><span class="lineno"> 128 </span>
<span class="lineno"> 129 </span>   <span class="comment">//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~取得image</span></pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/FireForumListActivity2.java">../../src/main/java/com/example/bottomnavigationview/FireForumListActivity2.java</a>:134</span>: <span class="message">Value must be &#8805; 0 but <code>getColumnIndex</code> can be -1</span><br /><pre class="errorlines">
<span class="lineno"> 131 </span>  cur.moveToFirst();
<span class="lineno"> 132 </span>
<span class="lineno"> 133 </span>  <span class="keyword">for</span>(<span class="keyword">int</span> i=<span class="number">0</span>; i&lt;rows_num; i++) {<span class="comment">//===========================不同食材名稱 寫入陣列========================================</span>
<span class="caretline"><span class="lineno"> 134 </span>       kind[i] =cur.getString(<span class="error">cur.getColumnIndex(<span class="string">"finekind"</span>)</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 135 </span>
<span class="lineno"> 136 </span>      <span class="comment">//將指標移至下一筆資料</span>
<span class="lineno"> 137 </span>      cur.moveToNext();
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/PreVegetables.java">../../src/main/java/com/example/bottomnavigationview/PreVegetables.java</a>:119</span>: <span class="message">Value must be &#8805; 0 but <code>getColumnIndex</code> can be -1</span><br /><pre class="errorlines">
<span class="lineno"> 116 </span>
<span class="lineno"> 117 </span>  <span class="keyword">do</span> {            <span class="comment">////~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~迴圈</span>
<span class="lineno"> 118 </span>
<span class="caretline"><span class="lineno"> 119 </span>      txvKind = cursor.getString(<span class="error">cursor.getColumnIndex(<span class="string">"kind"</span>)</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 120 </span>      mListName[index++] = txvKind; <span class="comment">//臨時字串變數,放置集合用</span>
<span class="lineno"> 121 </span>
<span class="lineno"> 122 </span>      txvFineKind = cursor.getString(cursor.getColumnIndex(<span class="string">"finekind"</span>));
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/PreVegetables.java">../../src/main/java/com/example/bottomnavigationview/PreVegetables.java</a>:122</span>: <span class="message">Value must be &#8805; 0 but <code>getColumnIndex</code> can be -1</span><br /><pre class="errorlines">
<span class="lineno"> 119 </span>        txvKind = cursor.getString(cursor.getColumnIndex(<span class="string">"kind"</span>));
<span class="lineno"> 120 </span>        mListName[index++] = txvKind; <span class="comment">//臨時字串變數,放置集合用</span>
<span class="lineno"> 121 </span>
<span class="caretline"><span class="lineno"> 122 </span>        txvFineKind = cursor.getString(<span class="error">cursor.getColumnIndex(<span class="string">"finekind"</span>)</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 123 </span>        ebuydate = cursor.getString(cursor.getColumnIndex(<span class="string">"buydate"</span>));
<span class="lineno"> 124 </span>        esavedate = cursor.getString(cursor.getColumnIndex(<span class="string">"savedate"</span>));
<span class="lineno"> 125 </span>        eID = cursor.getString(cursor.getColumnIndex(<span class="string">"_id"</span>));
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/PreVegetables.java">../../src/main/java/com/example/bottomnavigationview/PreVegetables.java</a>:123</span>: <span class="message">Value must be &#8805; 0 but <code>getColumnIndex</code> can be -1</span><br /><pre class="errorlines">
<span class="lineno"> 120 </span>        mListName[index++] = txvKind; <span class="comment">//臨時字串變數,放置集合用</span>
<span class="lineno"> 121 </span>
<span class="lineno"> 122 </span>        txvFineKind = cursor.getString(cursor.getColumnIndex(<span class="string">"finekind"</span>));
<span class="caretline"><span class="lineno"> 123 </span>        ebuydate = cursor.getString(<span class="error">cursor.getColumnIndex(<span class="string">"buydate"</span>)</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 124 </span>        esavedate = cursor.getString(cursor.getColumnIndex(<span class="string">"savedate"</span>));
<span class="lineno"> 125 </span>        eID = cursor.getString(cursor.getColumnIndex(<span class="string">"_id"</span>));
<span class="lineno"> 126 </span>        txvPart= cursor.getString(cursor.getColumnIndex(<span class="string">"part"</span>));
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/PreVegetables.java">../../src/main/java/com/example/bottomnavigationview/PreVegetables.java</a>:124</span>: <span class="message">Value must be &#8805; 0 but <code>getColumnIndex</code> can be -1</span><br /><pre class="errorlines">
<span class="lineno"> 121 </span>
<span class="lineno"> 122 </span>        txvFineKind = cursor.getString(cursor.getColumnIndex(<span class="string">"finekind"</span>));
<span class="lineno"> 123 </span>        ebuydate = cursor.getString(cursor.getColumnIndex(<span class="string">"buydate"</span>));
<span class="caretline"><span class="lineno"> 124 </span>        esavedate = cursor.getString(<span class="error">cursor.getColumnIndex(<span class="string">"savedate"</span>)</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 125 </span>        eID = cursor.getString(cursor.getColumnIndex(<span class="string">"_id"</span>));
<span class="lineno"> 126 </span>        txvPart= cursor.getString(cursor.getColumnIndex(<span class="string">"part"</span>));
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/PreVegetables.java">../../src/main/java/com/example/bottomnavigationview/PreVegetables.java</a>:125</span>: <span class="message">Value must be &#8805; 0 but <code>getColumnIndex</code> can be -1</span><br /><pre class="errorlines">
<span class="lineno"> 122 </span>   txvFineKind = cursor.getString(cursor.getColumnIndex(<span class="string">"finekind"</span>));
<span class="lineno"> 123 </span>   ebuydate = cursor.getString(cursor.getColumnIndex(<span class="string">"buydate"</span>));
<span class="lineno"> 124 </span>   esavedate = cursor.getString(cursor.getColumnIndex(<span class="string">"savedate"</span>));
<span class="caretline"><span class="lineno"> 125 </span>   eID = cursor.getString(<span class="error">cursor.getColumnIndex(<span class="string">"_id"</span>)</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 126 </span>   txvPart= cursor.getString(cursor.getColumnIndex(<span class="string">"part"</span>));
<span class="lineno"> 127 </span>
<span class="lineno"> 128 </span>   <span class="comment">//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~取得image</span></pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/PreVegetables.java">../../src/main/java/com/example/bottomnavigationview/PreVegetables.java</a>:126</span>: <span class="message">Value must be &#8805; 0 but <code>getColumnIndex</code> can be -1</span><br /><pre class="errorlines">
<span class="lineno"> 123 </span>   ebuydate = cursor.getString(cursor.getColumnIndex(<span class="string">"buydate"</span>));
<span class="lineno"> 124 </span>   esavedate = cursor.getString(cursor.getColumnIndex(<span class="string">"savedate"</span>));
<span class="lineno"> 125 </span>   eID = cursor.getString(cursor.getColumnIndex(<span class="string">"_id"</span>));
<span class="caretline"><span class="lineno"> 126 </span>   txvPart= cursor.getString(<span class="error">cursor.getColumnIndex(<span class="string">"part"</span>)</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 127 </span>
<span class="lineno"> 128 </span>   <span class="comment">//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~取得image</span>
<span class="lineno"> 129 </span>   imgArr2 = Base64.decode(cursor.getString(<span class="number">9</span>), Base64.DEFAULT);
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/VGanalysis.java">../../src/main/java/com/example/bottomnavigationview/VGanalysis.java</a>:118</span>: <span class="message">Value must be &#8805; 0 but <code>getColumnIndex</code> can be -1</span><br /><pre class="errorlines">
<span class="lineno"> 115 </span>
<span class="lineno"> 116 </span>  <span class="keyword">do</span> {            <span class="comment">////~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~迴圈</span>
<span class="lineno"> 117 </span>
<span class="caretline"><span class="lineno"> 118 </span>      txvKind = cursor.getString(<span class="error">cursor.getColumnIndex(<span class="string">"kind"</span>)</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 119 </span>      mListName[index++] = txvKind; <span class="comment">//臨時字串變數,放置集合用</span>
<span class="lineno"> 120 </span>
<span class="lineno"> 121 </span>      txvFineKind = cursor.getString(cursor.getColumnIndex(<span class="string">"finekind"</span>));
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/VGanalysis.java">../../src/main/java/com/example/bottomnavigationview/VGanalysis.java</a>:121</span>: <span class="message">Value must be &#8805; 0 but <code>getColumnIndex</code> can be -1</span><br /><pre class="errorlines">
<span class="lineno"> 118 </span>                txvKind = cursor.getString(cursor.getColumnIndex(<span class="string">"kind"</span>));
<span class="lineno"> 119 </span>                mListName[index++] = txvKind; <span class="comment">//臨時字串變數,放置集合用</span>
<span class="lineno"> 120 </span>
<span class="caretline"><span class="lineno"> 121 </span>                txvFineKind = cursor.getString(<span class="error">cursor.getColumnIndex(<span class="string">"finekind"</span>)</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 122 </span>                ebuydate = cursor.getString(cursor.getColumnIndex(<span class="string">"buydate"</span>));
<span class="lineno"> 123 </span>                esavedate = cursor.getString(cursor.getColumnIndex(<span class="string">"savedate"</span>));
<span class="lineno"> 124 </span>                eID = cursor.getString(cursor.getColumnIndex(<span class="string">"_id"</span>));
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/VGanalysis.java">../../src/main/java/com/example/bottomnavigationview/VGanalysis.java</a>:122</span>: <span class="message">Value must be &#8805; 0 but <code>getColumnIndex</code> can be -1</span><br /><pre class="errorlines">
<span class="lineno"> 119 </span>                mListName[index++] = txvKind; <span class="comment">//臨時字串變數,放置集合用</span>
<span class="lineno"> 120 </span>
<span class="lineno"> 121 </span>                txvFineKind = cursor.getString(cursor.getColumnIndex(<span class="string">"finekind"</span>));
<span class="caretline"><span class="lineno"> 122 </span>                ebuydate = cursor.getString(<span class="error">cursor.getColumnIndex(<span class="string">"buydate"</span>)</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 123 </span>                esavedate = cursor.getString(cursor.getColumnIndex(<span class="string">"savedate"</span>));
<span class="lineno"> 124 </span>                eID = cursor.getString(cursor.getColumnIndex(<span class="string">"_id"</span>));
<span class="lineno"> 125 </span>                txvPart= cursor.getString(cursor.getColumnIndex(<span class="string">"part"</span>));
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/VGanalysis.java">../../src/main/java/com/example/bottomnavigationview/VGanalysis.java</a>:123</span>: <span class="message">Value must be &#8805; 0 but <code>getColumnIndex</code> can be -1</span><br /><pre class="errorlines">
<span class="lineno"> 120 </span>
<span class="lineno"> 121 </span>                txvFineKind = cursor.getString(cursor.getColumnIndex(<span class="string">"finekind"</span>));
<span class="lineno"> 122 </span>                ebuydate = cursor.getString(cursor.getColumnIndex(<span class="string">"buydate"</span>));
<span class="caretline"><span class="lineno"> 123 </span>                esavedate = cursor.getString(<span class="error">cursor.getColumnIndex(<span class="string">"savedate"</span>)</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 124 </span>                eID = cursor.getString(cursor.getColumnIndex(<span class="string">"_id"</span>));
<span class="lineno"> 125 </span>                txvPart= cursor.getString(cursor.getColumnIndex(<span class="string">"part"</span>));
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/VGanalysis.java">../../src/main/java/com/example/bottomnavigationview/VGanalysis.java</a>:124</span>: <span class="message">Value must be &#8805; 0 but <code>getColumnIndex</code> can be -1</span><br /><pre class="errorlines">
<span class="lineno"> 121 </span>           txvFineKind = cursor.getString(cursor.getColumnIndex(<span class="string">"finekind"</span>));
<span class="lineno"> 122 </span>           ebuydate = cursor.getString(cursor.getColumnIndex(<span class="string">"buydate"</span>));
<span class="lineno"> 123 </span>           esavedate = cursor.getString(cursor.getColumnIndex(<span class="string">"savedate"</span>));
<span class="caretline"><span class="lineno"> 124 </span>           eID = cursor.getString(<span class="error">cursor.getColumnIndex(<span class="string">"_id"</span>)</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 125 </span>           txvPart= cursor.getString(cursor.getColumnIndex(<span class="string">"part"</span>));
<span class="lineno"> 126 </span>
<span class="lineno"> 127 </span>           <span class="comment">//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~取得image</span></pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/VGanalysis.java">../../src/main/java/com/example/bottomnavigationview/VGanalysis.java</a>:125</span>: <span class="message">Value must be &#8805; 0 but <code>getColumnIndex</code> can be -1</span><br /><pre class="errorlines">
<span class="lineno"> 122 </span>           ebuydate = cursor.getString(cursor.getColumnIndex(<span class="string">"buydate"</span>));
<span class="lineno"> 123 </span>           esavedate = cursor.getString(cursor.getColumnIndex(<span class="string">"savedate"</span>));
<span class="lineno"> 124 </span>           eID = cursor.getString(cursor.getColumnIndex(<span class="string">"_id"</span>));
<span class="caretline"><span class="lineno"> 125 </span>           txvPart= cursor.getString(<span class="error">cursor.getColumnIndex(<span class="string">"part"</span>)</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 126 </span>
<span class="lineno"> 127 </span>           <span class="comment">//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~取得image</span>
<span class="lineno"> 128 </span>           imgArr2 = Base64.decode(cursor.getString(<span class="number">9</span>), Base64.DEFAULT);
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationRange" style="display: none;">
Some parameters are required to in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length.<br/>To suppress this error, use the issue id "Range" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">Range</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRangeLink" onclick="reveal('explanationRange');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RangeCardLink" onclick="hideid('RangeCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ShowToast"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ShowToastCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Toast created but not shown</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/BuyActivity.java">../../src/main/java/com/example/bottomnavigationview/BuyActivity.java</a>:514</span>: <span class="message">Toast created but not shown: did you forget to call <code>show()</code>?</span><br /><pre class="errorlines">
<span class="lineno"> 511 </span>  <span class="keyword">try</span> {
<span class="lineno"> 512 </span>      bmp = BitmapFactory.decodeStream(getContentResolver().openInputStream(imgUri), <span class="keyword">null</span>, option);  <span class="comment">//採用調整後的Option來解碼~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~bmp~~~~~~~~~~~~~~~~~~~</span>
<span class="lineno"> 513 </span>  } <span class="keyword">catch</span> (IOException e) {
<span class="caretline"><span class="lineno"> 514 </span>      <span class="warning">Toast.makeText</span>(<span class="keyword">this</span>, <span class="string">"無法取得照片"</span>, Toast.LENGTH_LONG);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 515 </span>  }
<span class="lineno"> 516 </span>
<span class="lineno"> 517 </span>  <span class="keyword">if</span> (needRotate) {
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/FragmentList.java">../../src/main/java/com/example/bottomnavigationview/FragmentList.java</a>:37</span>: <span class="message">Toast created but not shown: did you forget to call <code>show()</code>?</span><br /><pre class="errorlines">
<span class="lineno"> 34 </span>
<span class="lineno"> 35 </span>    <span class="annotation">@Override</span>
<span class="lineno"> 36 </span>    <span class="keyword">public</span> <span class="keyword">void</span> onItemClick(AdapterView&lt;?> parent, View view, <span class="keyword">int</span> position, <span class="keyword">long</span> id) {
<span class="caretline"><span class="lineno"> 37 </span>        <span class="warning">Toast.makeText</span>(getActivity(),<span class="string">"Item"</span>+position,Toast.LENGTH_LONG);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 38 </span>
<span class="lineno"> 39 </span>    }
<span class="lineno"> 40 </span>}
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationShowToast" style="display: none;">
<code>Toast.makeText()</code> creates a <code>Toast</code> but does <b>not</b> show it. You must call <code>show()</code> on the resulting object to actually make the <code>Toast</code> appear.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ShowToast" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ShowToast</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationShowToastLink" onclick="reveal('explanationShowToast');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ShowToastCardLink" onclick="hideid('ShowToastCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="SimpleDateFormat"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SimpleDateFormatCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Implied locale in date format</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/FireDiscActivity2.java">../../src/main/java/com/example/bottomnavigationview/FireDiscActivity2.java</a>:135</span>: <span class="message">To get local formatting use <code>getDateInstance()</code>, <code>getDateTimeInstance()</code>, or <code>getTimeInstance()</code>, or use <code>new SimpleDateFormat(String template, Locale locale)</code> with for example <code>Locale.US</code> for ASCII dates.</span><br /><pre class="errorlines">
<span class="lineno"> 132 </span>            ((TextView)myView.findViewById(R.id.disccontent)).setText(s.content);
<span class="lineno"> 133 </span>            ((TextView)myView.findViewById(R.id.nickname)).setText(s.nickname);
<span class="lineno"> 134 </span>            Date date = <span class="keyword">new</span> Date(s.date);
<span class="caretline"><span class="lineno"> 135 </span>            SimpleDateFormat simpleDateFormat = <span class="warning"><span class="keyword">new</span> SimpleDateFormat(<span class="string">"MM/dd HH:mm"</span>)</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 136 </span>            simpleDateFormat.setTimeZone(TimeZone.getDefault());
<span class="lineno"> 137 </span>            String dateString = simpleDateFormat.format(date);
<span class="lineno"> 138 </span>            ((TextView)myView.findViewById(R.id.disctime)).setText(dateString);
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/FireForumListActivity2.java">../../src/main/java/com/example/bottomnavigationview/FireForumListActivity2.java</a>:272</span>: <span class="message">To get local formatting use <code>getDateInstance()</code>, <code>getDateTimeInstance()</code>, or <code>getTimeInstance()</code>, or use <code>new SimpleDateFormat(String template, Locale locale)</code> with for example <code>Locale.US</code> for ASCII dates.</span><br /><pre class="errorlines">
<span class="lineno"> 269 </span>
<span class="lineno"> 270 </span>    <span class="comment">//~~~~~~~~~~~~~~~~~時間設定</span>
<span class="lineno"> 271 </span>    Date lastUpdateddate = <span class="keyword">new</span> Date(s.lastUpdateDate);
<span class="caretline"><span class="lineno"> 272 </span>    SimpleDateFormat simpleDateFormat = <span class="warning"><span class="keyword">new</span> SimpleDateFormat(<span class="string">"MM/dd HH:mm"</span>)</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 273 </span>    simpleDateFormat.setTimeZone(TimeZone.getDefault());<span class="comment">//~~~~~~~~~取得默認時區~~~~~~~~~~~~~</span>
<span class="lineno"> 274 </span>    String dateString = simpleDateFormat.format(lastUpdateddate);
<span class="lineno"> 275 </span>            date.setText(dateString);
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationSimpleDateFormat" style="display: none;">
Almost all callers should use <code>getDateInstance()</code>, <code>getDateTimeInstance()</code>, or <code>getTimeInstance()</code> to get a ready-made instance of SimpleDateFormat suitable for the user's locale. The main reason you'd create an instance this class directly is because you need to format/parse a specific machine-readable format, in which case you almost certainly want to explicitly ask for US to ensure that you get ASCII digits (rather than, say, Arabic digits).<br/>
<br/>
Therefore, you should either use the form of the SimpleDateFormat constructor where you pass in an explicit locale, such as Locale.US, or use one of the get instance methods, or suppress this error if really know what you are doing.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/java/text/SimpleDateFormat.html">https://developer.android.com/reference/java/text/SimpleDateFormat.html</a>
</div>To suppress this error, use the issue id "SimpleDateFormat" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">SimpleDateFormat</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationSimpleDateFormatLink" onclick="reveal('explanationSimpleDateFormat');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SimpleDateFormatCardLink" onclick="hideid('SimpleDateFormatCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="InflateParams"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="InflateParamsCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Layout Inflation without a Parent</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/FireDiscActivity2.java">../../src/main/java/com/example/bottomnavigationview/FireDiscActivity2.java</a>:131</span>: <span class="message">Avoid passing <code>null</code> as the view root (needed to resolve layout parameters on the inflated layout's root element)</span><br /><pre class="errorlines">
<span class="lineno"> 128 </span>        <span class="keyword">public</span> View getView(<span class="keyword">int</span> position, View convertView, ViewGroup parent) {
<span class="lineno"> 129 </span>            <span class="keyword">final</span> FireDiscContent2 s = listForumDataAdapter.get(position);
<span class="lineno"> 130 </span>            LayoutInflater mInflater = getLayoutInflater();
<span class="caretline"><span class="lineno"> 131 </span>            View myView = mInflater.inflate(R.layout.disc_content2,<span class="warning"><span class="keyword">null</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 132 </span>            ((TextView)myView.findViewById(R.id.disccontent)).setText(s.content);
<span class="lineno"> 133 </span>            ((TextView)myView.findViewById(R.id.nickname)).setText(s.nickname);
<span class="lineno"> 134 </span>            Date date = <span class="keyword">new</span> Date(s.date);
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/FireForumListActivity2.java">../../src/main/java/com/example/bottomnavigationview/FireForumListActivity2.java</a>:258</span>: <span class="message">Avoid passing <code>null</code> as the view root (needed to resolve layout parameters on the inflated layout's root element)</span><br /><pre class="errorlines">
<span class="lineno"> 255 </span>  LayoutInflater mInflater = LayoutInflater.from(context);
<span class="lineno"> 256 </span>  LinearLayout itemlayout = <span class="keyword">null</span>;
<span class="lineno"> 257 </span>  <span class="keyword">if</span> (convertView == <span class="keyword">null</span>) {
<span class="caretline"><span class="lineno"> 258 </span>      itemlayout = (LinearLayout) mInflater.inflate(R.layout.forum_listitem2, <span class="warning"><span class="keyword">null</span></span>);  <span class="comment">// 決定XML</span></span>
<span class="lineno"> 259 </span>  } <span class="keyword">else</span> {
<span class="lineno"> 260 </span>      itemlayout = (LinearLayout) convertView;
<span class="lineno"> 261 </span>  }
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationInflateParams" style="display: none;">
When inflating a layout, avoid passing in null as the parent view, since otherwise any layout parameters on the root of the inflated layout will be ignored.<br/><div class="moreinfo">More info: <a href="https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/">https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/</a>
</div>To suppress this error, use the issue id "InflateParams" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">InflateParams</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationInflateParamsLink" onclick="reveal('explanationInflateParams');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="InflateParamsCardLink" onclick="hideid('InflateParamsCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="GradleDependency"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="GradleDependencyCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete Gradle Dependency</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle">../../build.gradle</a>:47</span>: <span class="message">A newer version of com.android.support.constraint:constraint-layout than 1.1.3 is available: 2.0.4</span><br /><pre class="errorlines">
<span class="lineno"> 44 </span>
<span class="lineno"> 45 </span>    <span class="comment">// Support Library (Legacy)
</span>
<span class="lineno"> 46 </span>    implementation <span class="string">'com.android.support:appcompat-v7:28.0.0'</span>
<span class="caretline"><span class="lineno"> 47 </span>    implementation <span class="warning"><span class="string">'com.android.support.constraint:constraint-layout:1.1.3'</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 48 </span>    implementation <span class="string">'com.android.support:multidex:1.0.3'</span>
<span class="lineno"> 49 </span>    implementation <span class="string">'com.android.support:cardview-v7:28.0.0'</span>
<span class="lineno"> 50 </span>    implementation <span class="string">'com.android.support:design:28.0.0'</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationGradleDependency" style="display: none;">
This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "GradleDependency" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">GradleDependency</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationGradleDependencyLink" onclick="reveal('explanationGradleDependency');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="GradleDependencyCardLink" onclick="hideid('GradleDependencyCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="SourceLockedOrientationActivity"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SourceLockedOrientationActivityCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Incompatible setRequestedOrientation value</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/BuyActivity.java">../../src/main/java/com/example/bottomnavigationview/BuyActivity.java</a>:101</span>: <span class="message">You should not lock orientation of your activities, so that you can support a good user experience for any device or orientation</span><br /><pre class="errorlines">
<span class="lineno">  98 </span>
<span class="lineno">  99 </span>  getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
<span class="lineno"> 100 </span>  setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_NOSENSOR);
<span class="caretline"><span class="lineno"> 101 </span>  <span class="warning">setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 102 </span>  imv = (ImageView) findViewById(R.id.imv);<span class="comment">//~~~~~~~~~照相用</span>
<span class="lineno"> 103 </span>  imv2 = (ImageView) findViewById(R.id.imv2);
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationSourceLockedOrientationActivity" style="display: none;">
The <code>Activity</code> should not be locked to a portrait orientation so that users can take advantage of the multi-window environments and larger landscape-first screens that Android runs on such as ChromeOS, tablets, and foldables. To fix the issue, consider calling <code>setRequestedOrientation</code> with the <code>ActivityInfo.SCREEN_ORIENTATION_FULL_SENSOR</code> or <code>ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED</code> options or removing the call all together.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/large-screens/large-screen-cookbook#restricted_app_orientation">https://developer.android.com/guide/topics/large-screens/large-screen-cookbook#restricted_app_orientation</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "SourceLockedOrientationActivity" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">SourceLockedOrientationActivity</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationSourceLockedOrientationActivityLink" onclick="reveal('explanationSourceLockedOrientationActivity');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SourceLockedOrientationActivityCardLink" onclick="hideid('SourceLockedOrientationActivityCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="SpUsage"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SpUsageCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using dp instead of sp for text sizes</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/epic_popup_positive.xml">../../src/main/res/layout/epic_popup_positive.xml</a>:50</span>: <span class="message">Should use "<code>sp</code>" instead of "<code>dp</code>" for text sizes</span><br /><pre class="errorlines">
<span class="lineno">  47 </span>                    <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"20dp"</span>
<span class="lineno">  48 </span>                    <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"20dp"</span>
<span class="lineno">  49 </span>                    <span class="prefix">android:</span><span class="attribute">textAlignment</span>=<span class="value">"center"</span>
<span class="caretline"><span class="lineno">  50 </span>                    <span class="warning"><span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"25dp"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  51 </span>                    <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
<span class="lineno">  52 </span>                    <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"@string/positive_popup_title_default"</span>/>
<span class="lineno">  53 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_positive.xml">../../src/main/res/layout/epic_popup_positive.xml</a>:70</span>: <span class="message">Should use "<code>sp</code>" instead of "<code>dp</code>" for text sizes</span><br /><pre class="errorlines">
<span class="lineno">  67 </span>                   <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"20dp"</span>
<span class="lineno">  68 </span>                   <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"20dp"</span>
<span class="lineno">  69 </span>                   <span class="prefix">android:</span><span class="attribute">textAlignment</span>=<span class="value">"center"</span>
<span class="caretline"><span class="lineno">  70 </span>                   <span class="warning"><span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18dp"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  71 </span>                   <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"@string/positive_popup_text_default"</span>><span class="tag">&lt;/TextView></span>
<span class="lineno">  72 </span>
<span class="lineno">  73 </span>               <span class="tag">&lt;TextView</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_positive.xml">../../src/main/res/layout/epic_popup_positive.xml</a>:80</span>: <span class="message">Should use "<code>sp</code>" instead of "<code>dp</code>" for text sizes</span><br /><pre class="errorlines">
<span class="lineno">  77 </span>                    <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"20dp"</span>
<span class="lineno">  78 </span>                    <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"10dp"</span>
<span class="lineno">  79 </span>                    <span class="prefix">android:</span><span class="attribute">textAlignment</span>=<span class="value">"center"</span>
<span class="caretline"><span class="lineno">  80 </span>                    <span class="warning"><span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"20dp"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  81 </span>                    <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
<span class="lineno">  82 </span>                    <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"+57 points"</span>><span class="tag">&lt;/TextView></span>
<span class="lineno">  83 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/forum_listitem2.xml">../../src/main/res/layout/forum_listitem2.xml</a>:63</span>: <span class="message">Should use "<code>sp</code>" instead of "<code>dp</code>" for text sizes</span><br /><pre class="errorlines">
<span class="lineno"> 60 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 61 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 62 </span>            <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Subject"</span>
<span class="caretline"><span class="lineno"> 63 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"22dp"</span></span>><span class="tag">&lt;/TextView></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 64 </span>
<span class="lineno"> 65 </span>    <span class="tag">&lt;/LinearLayout></span>
<span class="lineno"> 66 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/item.xml">../../src/main/res/layout/item.xml</a>:28</span>: <span class="message">Should use "<code>sp</code>" instead of "<code>dp</code>" for text sizes</span><br /><pre class="errorlines">
<span class="lineno"> 25 </span>            <span class="prefix">android:</span><span class="attribute">layout_toRightOf</span>=<span class="value">"@+id/photo"</span>
<span class="lineno"> 26 </span>            <span class="prefix">android:</span><span class="attribute">ellipsize</span>=<span class="value">"marquee"</span>
<span class="lineno"> 27 </span>            <span class="prefix">android:</span><span class="attribute">singleLine</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno"> 28 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"20dip"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 29 </span>    <span class="tag">&lt;/RelativeLayout></span>
<span class="lineno"> 30 </span>
<span class="lineno"> 31 </span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationSpUsage" style="display: none;">
When setting text sizes, you should normally use <code>sp</code>, or "scale-independent pixels". This is like the <code>dp</code> unit, but it is also scaled by the user's font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user's preference.<br/>
<br/>
There <b>are</b> cases where you might need to use <code>dp</code>; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user's font size settings are not respected, so consider adjusting the layout itself to be more flexible.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/multiscreen/screendensities.html">https://developer.android.com/training/multiscreen/screendensities.html</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "SpUsage" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">SpUsage</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationSpUsageLink" onclick="reveal('explanationSpUsage');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SpUsageCardLink" onclick="hideid('SpUsageCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Correctness:Messages"></a>
<a name="Typos"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="TyposCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Spelling error</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:50</span>: <span class="message">"Wanna" is a common misspelling; did you mean "Want to"?</span><br /><pre class="errorlines">
<span class="lineno"> 47 </span>
<span class="lineno"> 48 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"positive_popup_text_default"</span>>You have successfully completed the challenge. Keep it up to earn more points.<span class="tag">&lt;/string></span>
<span class="lineno"> 49 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"positive_popup_title_default"</span>>Like a boss!<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 50 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"negative_popup_text_defaulte"</span>>You have failed to complete the challenge. So close! <span class="warning">Wanna</span> have another go at it?<span class="tag">&lt;/string></span>
</span>
<span class="lineno"> 51 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"negative_popup_title_defaulte"</span>>Awww.... Snap!<span class="tag">&lt;/string></span>
<span class="lineno"> 52 </span>
<span class="lineno"> 53 </span><span class="tag">&lt;/resources></span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationTypos" style="display: none;">
This check looks through the string definitions, and if it finds any words that look like likely misspellings, they are flagged.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "Typos" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">Typos</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Messages</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 7/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationTyposLink" onclick="reveal('explanationTypos');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="TyposCardLink" onclick="hideid('TyposCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Performance"></a>
<a name="Recycle"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RecycleCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Missing recycle() calls</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/BuyMoney.java">../../src/main/java/com/example/bottomnavigationview/BuyMoney.java</a>:91</span>: <span class="message">This <code>Cursor</code> should be freed up after use with <code>#close()</code></span><br /><pre class="errorlines">
<span class="lineno">  88 </span>
<span class="lineno">  89 </span>    SQLiteDatabase db = openOrCreateDatabase(DB_NAME, Context.MODE_PRIVATE, <span class="keyword">null</span>);
<span class="lineno">  90 </span>
<span class="caretline"><span class="lineno">  91 </span>    Cursor cursor = db.<span class="warning">rawQuery</span>(<span class="string">"SELECT * FROM "</span>+ TB_NAME,<span class="keyword">null</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  92 </span>    <span class="keyword">int</span> num = cursor.getCount();
<span class="lineno">  93 </span>
<span class="lineno">  94 </span>    <span class="keyword">if</span> (num > <span class="number">0</span>) {
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/PreVegetables.java">../../src/main/java/com/example/bottomnavigationview/PreVegetables.java</a>:95</span>: <span class="message">This <code>Cursor</code> should be freed up after use with <code>#close()</code></span><br /><pre class="errorlines">
<span class="lineno">  92 </span>
<span class="lineno">  93 </span>  <span class="comment">//Cursor cursor = db.rawQuery("SELECT * FROM hotlist where _id=?",new String[] {"3"}); 篩選成功---------單一</span>
<span class="lineno">  94 </span>  <span class="comment">// Cursor cursor = db.rawQuery("SELECT * FROM hotlist where buyprice=23",null);篩選成功--------多筆</span>
<span class="caretline"><span class="lineno">  95 </span>  Cursor cursor = db.<span class="warning">rawQuery</span>(<span class="string">"SELECT * FROM "</span>+ TB_NAME,<span class="keyword">null</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  96 </span>  <span class="keyword">int</span> num = cursor.getCount();
<span class="lineno">  97 </span>
<span class="lineno">  98 </span>  <span class="keyword">if</span> (num > <span class="number">0</span>) {
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/VGanalysis.java">../../src/main/java/com/example/bottomnavigationview/VGanalysis.java</a>:89</span>: <span class="message">This <code>Cursor</code> should be freed up after use with <code>#close()</code></span><br /><pre class="errorlines">
<span class="lineno">  86 </span>  <span class="keyword">protected</span> <span class="keyword">void</span> GetCommonUsers()  {
<span class="lineno">  87 </span>
<span class="lineno">  88 </span>      SQLiteDatabase db = openOrCreateDatabase(DB_NAME, Context.MODE_PRIVATE, <span class="keyword">null</span>);
<span class="caretline"><span class="lineno">  89 </span>      Cursor cursor = db.<span class="warning">rawQuery</span>(<span class="string">"SELECT * FROM hotlist GROUP BY finekind HAVING count(*)>0 "</span>,<span class="keyword">null</span>);</span>
<span class="lineno">  90 </span>      <span class="comment">// Cursor cursor = db.rawQuery("SELECT * FROM hotlist where buyprice=23",null);</span>
<span class="lineno">  91 </span>
<span class="lineno">  92 </span>      <span class="comment">//Cursor cursor = db.rawQuery("SELECT * FROM hotlist where _id=?",new String[] {"3"}); 篩選成功---------單一</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationRecycle" style="display: none;">
Many resources, such as TypedArrays, VelocityTrackers, etc., should be recycled (with a <code>recycle()</code> call) after use. This lint check looks for missing <code>recycle()</code> calls.<br/>To suppress this error, use the issue id "Recycle" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">Recycle</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 7/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRecycleLink" onclick="reveal('explanationRecycle');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RecycleCardLink" onclick="hideid('RecycleCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ViewHolder"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ViewHolderCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">View Holder Candidates</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/FireDiscActivity2.java">../../src/main/java/com/example/bottomnavigationview/FireDiscActivity2.java</a>:131</span>: <span class="message">Unconditional layout inflation from view adapter: Should use View Holder pattern (use recycled view passed into this method as the second parameter) for smoother scrolling</span><br /><pre class="errorlines">
<span class="lineno"> 128 </span>        <span class="keyword">public</span> View getView(<span class="keyword">int</span> position, View convertView, ViewGroup parent) {
<span class="lineno"> 129 </span>            <span class="keyword">final</span> FireDiscContent2 s = listForumDataAdapter.get(position);
<span class="lineno"> 130 </span>            LayoutInflater mInflater = getLayoutInflater();
<span class="caretline"><span class="lineno"> 131 </span>            View myView = <span class="warning">mInflater.inflate(R.layout.disc_content2,<span class="keyword">null</span>)</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 132 </span>            ((TextView)myView.findViewById(R.id.disccontent)).setText(s.content);
<span class="lineno"> 133 </span>            ((TextView)myView.findViewById(R.id.nickname)).setText(s.nickname);
<span class="lineno"> 134 </span>            Date date = <span class="keyword">new</span> Date(s.date);
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationViewHolder" style="display: none;">
When implementing a view Adapter, you should avoid unconditionally inflating a new layout; if an available item is passed in for reuse, you should try to use that one instead. This helps make for example <code>ListView</code> scrolling much smoother.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/ui/layout/recyclerview#ViewHolder">https://developer.android.com/guide/topics/ui/layout/recyclerview#ViewHolder</a>
</div>To suppress this error, use the issue id "ViewHolder" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ViewHolder</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationViewHolderLink" onclick="reveal('explanationViewHolder');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ViewHolderCardLink" onclick="hideid('ViewHolderCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="HandlerLeak"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="HandlerLeakCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Handler reference leaks</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/FireForumListActivity2.java">../../src/main/java/com/example/bottomnavigationview/FireForumListActivity2.java</a>:72</span>: <span class="message">This <code>Handler</code> class should be static or leaks might occur (anonymous android.os.Handler)</span><br /><pre class="errorlines">
<span class="lineno">  69 </span><span class="lineno">  70 </span><span class="lineno">  71 </span>
<span class="caretline"><span class="lineno">  72 </span>  <span class="keyword">private</span> Handler handler = <span class="warning"><span class="keyword">new</span> Handler() {  <span class="comment">//~~~~~~~~~建立Handler物件才能使用handleMessage(Message msg)方法，</span></span></span>
<span class="lineno">  73 </span>      <span class="keyword">public</span> <span class="keyword">void</span> handleMessage(Message msg) {   <span class="comment">//~~~~~~~~~~~~Handler 經紀人(Thread 裡是無法做任何有關介面的事，所以我們必須倚賴 Handler，可以把它想成介面與執行緒之間的經紀人)</span>
<span class="lineno">  74 </span>          <span class="keyword">switch</span> (msg.what) {
<span class="lineno">  75 </span>              <span class="keyword">case</span> LIST_PETS: {
</pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/WelcomeActivty.java">../../src/main/java/com/example/bottomnavigationview/WelcomeActivty.java</a>:25</span>: <span class="message">This <code>Handler</code> class should be static or leaks might occur (anonymous android.os.Handler)</span><br /><pre class="errorlines">
<span class="lineno"> 22 </span>
<span class="lineno"> 23 </span>    }
<span class="lineno"> 24 </span>    <span class="keyword">private</span> <span class="keyword">static</span> <span class="keyword">final</span> <span class="keyword">int</span> GOTO_MAIN_ACTIVITY = <span class="number">0</span>;
<span class="caretline"><span class="lineno"> 25 </span>    <span class="keyword">private</span> Handler mHandler = <span class="warning"><span class="keyword">new</span> Handler() {</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 26 </span>        <span class="keyword">public</span> <span class="keyword">void</span> handleMessage(android.os.Message msg) {
<span class="lineno"> 27 </span>
<span class="lineno"> 28 </span>            <span class="keyword">switch</span> (msg.what) {
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationHandlerLeak" style="display: none;">
Since this Handler is declared as an inner class, it may prevent the outer class from being garbage collected. If the Handler is using a <code>Looper</code> or <code>MessageQueue</code> for a thread other than the main thread, then there is no issue. If the <code>Handler</code> is using the <code>Looper</code> or <code>MessageQueue</code> of the main thread, you need to fix your <code>Handler</code> declaration, as follows: Declare the <code>Handler</code> as a static class; In the outer class, instantiate a <code>WeakReference</code> to the outer class and pass this object to your <code>Handler</code> when you instantiate the <code>Handler</code>; Make all references to members of the outer class using the <code>WeakReference</code> object.<br/>To suppress this error, use the issue id "HandlerLeak" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">HandlerLeak</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationHandlerLeakLink" onclick="reveal('explanationHandlerLeak');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="HandlerLeakCardLink" onclick="hideid('HandlerLeakCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="DisableBaselineAlignment"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="DisableBaselineAlignmentCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Missing baselineAligned attribute</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/item2money.xml">../../src/main/res/layout/item2money.xml</a>:2</span>: <span class="message">Set <code>android:baselineAligned="false"</code> on this element for better performance</span><br /><pre class="errorlines">
<span class="lineno">   1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">   2 </span><span class="tag">&lt;</span><span class="warning"><span class="tag">LinearLayout</span></span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   3 </span>    <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="lineno">   4 </span>    <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno">   5 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationDisableBaselineAlignment" style="display: none;">
When a <code>LinearLayout</code> is used to distribute the space proportionally between nested layouts, the baseline alignment property should be turned off to make the layout computation faster.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "DisableBaselineAlignment" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">DisableBaselineAlignment</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationDisableBaselineAlignmentLink" onclick="reveal('explanationDisableBaselineAlignment');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="DisableBaselineAlignmentCardLink" onclick="hideid('DisableBaselineAlignmentCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="InefficientWeight"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="InefficientWeightCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Inefficient layout weight</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:98</span>: <span class="message">Use a <code>layout_width</code> of <code>0dp</code> instead of <code>256dp</code> for better performance</span><br /><pre class="errorlines">
<span class="lineno">  95 </span>
<span class="lineno">  96 </span>            <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno">  97 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/buydate"</span>
<span class="caretline"><span class="lineno">  98 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"256dp"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  99 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 100 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 101 </span>                <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center|center_horizontal|center_vertical"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:110</span>: <span class="message">Use a <code>layout_height</code> of <code>0dp</code> instead of <code>50dp</code> for better performance</span><br /><pre class="errorlines">
<span class="lineno"> 107 </span>
<span class="lineno"> 108 </span>        <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span><span class="lineno"> 109 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno"> 110 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"50dp"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 111 </span>            <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"0"</span>
<span class="lineno"> 112 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno"> 113 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:133</span>: <span class="message">Use a <code>layout_width</code> of <code>0dp</code> instead of <code>256dp</code> for better performance</span><br /><pre class="errorlines">
<span class="lineno"> 130 </span>
<span class="lineno"> 131 </span>            <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 132 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/savedate"</span>
<span class="caretline"><span class="lineno"> 133 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"256dp"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 134 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 135 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 136 </span>                <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center|center_horizontal|center_vertical"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_search.xml">../../src/main/res/layout/fragment_search.xml</a>:97</span>: <span class="message">Use a <code>layout_width</code> of <code>0dp</code> instead of <code>wrap_content</code> for better performance</span><br /><pre class="errorlines">
<span class="lineno">  94 </span>
<span class="lineno">  95 </span>            <span class="tag">&lt;EditText</span><span class="attribute">
</span><span class="lineno">  96 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/editText"</span>
<span class="caretline"><span class="lineno">  97 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  98 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  99 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 100 </span>                <span class="prefix">android:</span><span class="attribute">ems</span>=<span class="value">"10"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_search.xml">../../src/main/res/layout/fragment_search.xml</a>:119</span>: <span class="message">Use a <code>layout_width</code> of <code>0dp</code> instead of <code>wrap_content</code> for better performance</span><br /><pre class="errorlines">
<span class="lineno"> 116 </span>
<span class="lineno"> 117 </span>            <span class="tag">&lt;EditText</span><span class="attribute">
</span><span class="lineno"> 118 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/editText1"</span>
<span class="caretline"><span class="lineno"> 119 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 120 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 121 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 122 </span>                <span class="prefix">android:</span><span class="attribute">ems</span>=<span class="value">"10"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/item1.xml">../../src/main/res/layout/item1.xml</a>:133</span>: <span class="message">Use a <code>layout_height</code> of <code>0dp</code> instead of <code>match_parent</code> for better performance</span><br /><pre class="errorlines">
<span class="lineno"> 130 </span>        <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 131 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/savedate"</span>
<span class="lineno"> 132 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno"> 133 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 134 </span>            <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 135 </span>            <span class="prefix">tools:</span><span class="attribute">text</span>=<span class="value">"savedate"</span> />
<span class="lineno"> 136 </span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationInefficientWeight" style="display: none;">
When only a single widget in a <code>LinearLayout</code> defines a weight, it is more efficient to assign a width/height of <code>0dp</code> to it since it will absorb all the remaining space anyway. With a declared width/height of <code>0dp</code> it does not have to measure its own size first.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "InefficientWeight" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">InefficientWeight</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationInefficientWeightLink" onclick="reveal('explanationInefficientWeight');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="InefficientWeightCardLink" onclick="hideid('InefficientWeightCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="NestedWeights"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="NestedWeightsCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Nested layout weights</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:135</span>: <span class="message">Nested weights are bad for performance</span><br /><pre class="errorlines">
<span class="lineno"> 132 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/savedate"</span>
<span class="lineno"> 133 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"256dp"</span>
<span class="lineno"> 134 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno"> 135 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 136 </span>                <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center|center_horizontal|center_vertical"</span>
<span class="lineno"> 137 </span>                <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"2019-10-15"</span>
<span class="lineno"> 138 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/item1.xml">../../src/main/res/layout/item1.xml</a>:134</span>: <span class="message">Nested weights are bad for performance</span><br /><pre class="errorlines">
<span class="lineno"> 131 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/savedate"</span>
<span class="lineno"> 132 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 133 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno"> 134 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 135 </span>            <span class="prefix">tools:</span><span class="attribute">text</span>=<span class="value">"savedate"</span> />
<span class="lineno"> 136 </span>
<span class="lineno"> 137 </span>    <span class="tag">&lt;/LinearLayout></span>
</pre>

<span class="location"><a href="../../src/main/res/layout/item2money.xml">../../src/main/res/layout/item2money.xml</a>:126</span>: <span class="message">Nested weights are bad for performance</span><br /><pre class="errorlines">
<span class="lineno"> 123 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/savedate"</span>
<span class="lineno"> 124 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 125 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno"> 126 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"0.5"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 127 </span>            <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center"</span>
<span class="lineno"> 128 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/colorAccent"</span>
<span class="lineno"> 129 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationNestedWeights" style="display: none;">
Layout weights require a widget to be measured twice. When a <code>LinearLayout</code> with non-zero weights is nested inside another <code>LinearLayout</code> with non-zero weights, then the number of measurements increase exponentially.<br/>To suppress this error, use the issue id "NestedWeights" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">NestedWeights</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationNestedWeightsLink" onclick="reveal('explanationNestedWeights');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="NestedWeightsCardLink" onclick="hideid('NestedWeightsCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="Overdraw"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverdrawCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overdraw: Painting regions more than once</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:7</span>: <span class="message">Possible overdraw: Root element paints background <code>@android:color/holo_red_dark</code> with a theme that also paints a background (inferred theme is <code>@style/AppTheme</code>)</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>    <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno">  7 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@android:color/holo_red_dark"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>    <span class="prefix">tools:</span><span class="attribute">context</span>=<span class="value">".MainActivity"</span>>
<span class="lineno">  9 </span>
<span class="lineno"> 10 </span><span class="tag">&lt;FrameLayout</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/disc2.xml">../../src/main/res/layout/disc2.xml</a>:7</span>: <span class="message">Possible overdraw: Root element paints background <code>#ff85ff</code> with a theme that also paints a background (inferred theme is <code>@style/AppTheme</code>)</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>    <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno">  7 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"#ff85ff"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>    <span class="prefix">tools:</span><span class="attribute">context</span>=<span class="value">".DiscActivity"</span>>
<span class="lineno">  9 </span>
<span class="lineno"> 10 </span>    <span class="tag">&lt;TextView</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/disc_content2.xml">../../src/main/res/layout/disc_content2.xml</a>:6</span>: <span class="message">Possible overdraw: Root element paints background <code>#ffffff</code> with a theme that also paints a background (inferred theme is <code>@style/AppTheme</code>)</span><br /><pre class="errorlines">
<span class="lineno">  3 </span><span class="attribute">    </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
<span class="lineno">  4 </span>    <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="lineno">  5 </span>    <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="caretline"><span class="lineno">  6 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"#ffffff"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  7 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  8 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>>
<span class="lineno">  9 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fireforulist2.xml">../../src/main/res/layout/fireforulist2.xml</a>:7</span>: <span class="message">Possible overdraw: Root element paints background <code>#ffd47a</code> with a theme that also paints a background (inferred theme is <code>@style/AppTheme</code>)</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>    <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno">  7 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"#ffd47a"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>    <span class="prefix">tools:</span><span class="attribute">context</span>=<span class="value">".FireForumListActivity2"</span>>
<span class="lineno">  9 </span>
<span class="lineno"> 10 </span>    <span class="tag">&lt;Button</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/forum_listitem2.xml">../../src/main/res/layout/forum_listitem2.xml</a>:7</span>: <span class="message">Possible overdraw: Root element paints background <code>#ffffff</code> with a theme that also paints a background (inferred theme is <code>@style/AppTheme</code>)</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>    <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  7 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"#ffffff"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>    <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>>
<span class="lineno">  9 </span>
<span class="lineno"> 10 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/fragment_favorites.xml">../../src/main/res/layout/fragment_favorites.xml</a>:7</span>: <span class="message">Possible overdraw: Root element paints background <code>#fffc7a</code> with a theme that also paints a background (inferred theme is <code>@style/AppTheme</code>)</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>    <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno">  7 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"#fffc7a"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>    <span class="prefix">tools:</span><span class="attribute">context</span>=<span class="value">".MainActivity"</span>>
<span class="lineno">  9 </span>
<span class="lineno"> 10 </span>    <span class="tag">&lt;TextView</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/fragment_home.xml">../../src/main/res/layout/fragment_home.xml</a>:7</span>: <span class="message">Possible overdraw: Root element paints background <code>#009688</code> with a theme that also paints a background (inferred theme is <code>@style/AppTheme</code>)</span><br /><pre class="errorlines">
<span class="lineno">   4 </span>    <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno">   5 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">   6 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno">   7 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"#009688"</span></span>>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   8 </span>
<span class="lineno">   9 </span>    <span class="tag">&lt;Button</span><span class="attribute">
</span><span class="lineno">  10 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/BtBuy"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_search.xml">../../src/main/res/layout/fragment_search.xml</a>:6</span>: <span class="message">Possible overdraw: Root element paints background <code>@android:color/holo_green_light</code> with a theme that also paints a background (inferred theme is <code>@style/AppTheme</code>)</span><br /><pre class="errorlines">
<span class="lineno">   3 </span>    <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="lineno">   4 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">   5 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno">   6 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@android:color/holo_green_light"</span></span>>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   7 </span>
<span class="lineno">   8 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span><span class="lineno">   9 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationOverdraw" style="display: none;">
If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called "overdraw".<br/>
<br/>
NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it's currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.<br/>
<br/>
If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.<br/>
<br/>
Of course it's possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead.<br/>To suppress this error, use the issue id "Overdraw" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">Overdraw</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationOverdrawLink" onclick="reveal('explanationOverdraw');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverdrawCardLink" onclick="hideid('OverdrawCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UnusedResources"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnusedResourcesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Unused resources</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/drawable/cookbear.jpg">../../src/main/res/drawable/cookbear.jpg</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/cookbear.jpg" /><span class="message">The resource <code>R.drawable.cookbear</code> appears to be unused</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/ic_favorite_black_24dp.xml">../../src/main/res/drawable/ic_favorite_black_24dp.xml</a>:1</span>: <span class="message">The resource <code>R.drawable.ic_favorite_black_24dp</code> appears to be unused</span><br /><pre class="errorlines">
<span class="caretline"><span class="lineno">  1 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  2 </span>        <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  3 </span>        <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>        <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"24.0"</span></pre>

<span class="location"><a href="../../src/main/res/drawable/ic_launch_black_24dp.xml">../../src/main/res/drawable/ic_launch_black_24dp.xml</a>:1</span>: <span class="message">The resource <code>R.drawable.ic_launch_black_24dp</code> appears to be unused</span><br /><pre class="errorlines">
<span class="caretline"><span class="lineno">  1 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  2 </span>        <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  3 </span>        <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>        <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"24.0"</span></pre>

<span class="location"><a href="../../src/main/res/mipmap-anydpi-v26/ic_launcher.xml">../../src/main/res/mipmap-anydpi-v26/ic_launcher.xml</a>:2</span>: <span class="message">The resource <code>R.mipmap.ic_launcher</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno"> 2 </span><span class="warning"><span class="tag">&lt;adaptive-icon</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 3 </span>    <span class="tag">&lt;background</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">drawable</span>=<span class="value">"@drawable/ic_launcher_background"</span> />
<span class="lineno"> 4 </span>    <span class="tag">&lt;foreground</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">drawable</span>=<span class="value">"@drawable/ic_launcher_foreground"</span> />
<span class="lineno"> 5 </span><span class="tag">&lt;/adaptive-icon></span></pre>

<span class="location"><a href="../../src/main/res/layout/item.xml">../../src/main/res/layout/item.xml</a>:8</span>: <span class="message">The resource <code>R.layout.item</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  5 </span>
<span class="lineno">  6 </span>
<span class="lineno">  7 </span>
<span class="caretline"><span class="lineno">  8 </span>    <span class="warning"><span class="tag">&lt;RelativeLayout</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  9 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"fill_parent"</span>
<span class="lineno"> 10 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"?android:attr/listPreferredItemHeight"</span>>
<span class="lineno"> 11 </span>        <span class="tag">&lt;ImageView</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/photo"</span>
</pre>

<span class="location"><a href="../../src/main/res/drawable/speech_bubble.png">../../src/main/res/drawable/speech_bubble.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/speech_bubble.png" /><span class="message">The resource <code>R.drawable.speech_bubble</code> appears to be unused</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:2</span>: <span class="message">The resource <code>R.string.app_name</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="tag">&lt;resources></span>
<span class="caretline"><span class="lineno">  2 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"app_name"</span></span>>MyVG0809<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>    <span class="tag">&lt;string-array</span><span class="attribute"> name</span>=<span class="value">"array"</span>>
<span class="lineno">  4 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"Batman"</span>><span class="tag">&lt;/item></span>
<span class="lineno">  5 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"Superman"</span>><span class="tag">&lt;/item></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:30</span>: <span class="message">The resource <code>R.array.weekday</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 27 </span>
<span class="lineno"> 28 </span>    <span class="tag">&lt;/string-array></span>
<span class="lineno"> 29 </span>
<span class="caretline"><span class="lineno"> 30 </span>    <span class="tag">&lt;string-array</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"weekday"</span></span>>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 31 </span>        <span class="tag">&lt;item></span>星期日<span class="tag">&lt;/item></span>
<span class="lineno"> 32 </span>        <span class="tag">&lt;item></span>星期一<span class="tag">&lt;/item></span>
<span class="lineno"> 33 </span>        <span class="tag">&lt;item></span>星期二<span class="tag">&lt;/item></span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUnusedResources" style="display: none;">
Unused resources make applications larger and slow down builds.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
,<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UnusedResources" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnusedResources</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnusedResourcesLink" onclick="reveal('explanationUnusedResources');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnusedResourcesCardLink" onclick="hideid('UnusedResourcesCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UselessLeaf"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UselessLeafCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Unnecessary leaf layout</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:60</span>: <span class="message">This <code>LinearLayout</code> view is unnecessary (no children, no <code>background</code>, no <code>id</code>, no <code>style</code>)</span><br /><pre class="errorlines">
<span class="lineno">  57 </span>            <span class="prefix">app:</span><span class="attribute">layout_constraintTop_toTopOf</span>=<span class="value">"parent"</span>
<span class="lineno">  58 </span>            <span class="prefix">tools:</span><span class="attribute">layout_editor_absoluteX</span>=<span class="value">"3dp"</span>>
<span class="lineno">  59 </span>
<span class="caretline"><span class="lineno">  60 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">LinearLayout</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  61 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  62 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno">  63 </span>                <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUselessLeaf" style="display: none;">
A layout that has no children or no background can often be removed (since it is invisible) for a flatter and more efficient layout hierarchy.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UselessLeaf" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UselessLeaf</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 2/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUselessLeafLink" onclick="reveal('explanationUselessLeaf');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UselessLeafCardLink" onclick="hideid('UselessLeafCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UselessParent"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UselessParentCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Unnecessary parent layout</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_buymoney.xml">../../src/main/res/layout/activity_buymoney.xml</a>:9</span>: <span class="message">This <code>LinearLayout</code> layout or its <code>RelativeLayout</code> parent is unnecessary</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno">  7 </span>    <span class="prefix">tools:</span><span class="attribute">context</span>=<span class="value">".PreVegetables"</span>>
<span class="lineno">  8 </span>
<span class="caretline"><span class="lineno">  9 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">LinearLayout</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 10 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 11 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 12 </span>        <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_pre_vegetables.xml">../../src/main/res/layout/activity_pre_vegetables.xml</a>:9</span>: <span class="message">This <code>LinearLayout</code> layout or its <code>RelativeLayout</code> parent is unnecessary</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno">  7 </span>    <span class="prefix">tools:</span><span class="attribute">context</span>=<span class="value">".PreVegetables"</span>>
<span class="lineno">  8 </span>
<span class="caretline"><span class="lineno">  9 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">LinearLayout</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 10 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 11 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 12 </span>        <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_vganalysis.xml">../../src/main/res/layout/activity_vganalysis.xml</a>:9</span>: <span class="message">This <code>LinearLayout</code> layout or its <code>RelativeLayout</code> parent is unnecessary</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno">  7 </span>    <span class="prefix">tools:</span><span class="attribute">context</span>=<span class="value">".PreVegetables"</span>>
<span class="lineno">  8 </span>
<span class="caretline"><span class="lineno">  9 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">LinearLayout</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 10 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 11 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 12 </span>        <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>>
</pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_negtive.xml">../../src/main/res/layout/epic_popup_negtive.xml</a>:36</span>: <span class="message">This <code>LinearLayout</code> layout or its <code>LinearLayout</code> parent is unnecessary</span><br /><pre class="errorlines">
<span class="lineno">  33 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  34 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>>
<span class="lineno">  35 </span>
<span class="caretline"><span class="lineno">  36 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">LinearLayout</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  37 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  38 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  39 </span>                <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_positive.xml">../../src/main/res/layout/epic_popup_positive.xml</a>:36</span>: <span class="message">This <code>LinearLayout</code> layout or its <code>LinearLayout</code> parent is unnecessary</span><br /><pre class="errorlines">
<span class="lineno">  33 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  34 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>>
<span class="lineno">  35 </span>
<span class="caretline"><span class="lineno">  36 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">LinearLayout</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  37 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  38 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  39 </span>                <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/forum_listitem2.xml">../../src/main/res/layout/forum_listitem2.xml</a>:10</span>: <span class="message">This <code>LinearLayout</code> layout or its <code>LinearLayout</code> parent is unnecessary; transfer the <code>background</code> attribute to the other view</span><br /><pre class="errorlines">
<span class="lineno">  7 </span>    <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"#ffffff"</span>
<span class="lineno">  8 </span>    <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>>
<span class="lineno">  9 </span>
<span class="caretline"><span class="lineno"> 10 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">LinearLayout</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 11 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 12 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 13 </span>        <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_search.xml">../../src/main/res/layout/fragment_search.xml</a>:8</span>: <span class="message">This <code>LinearLayout</code> layout or its <code>RelativeLayout</code> parent is unnecessary; transfer the <code>background</code> attribute to the other view</span><br /><pre class="errorlines">
<span class="lineno">   5 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno">   6 </span>    <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@android:color/holo_green_light"</span>>
<span class="lineno">   7 </span>
<span class="caretline"><span class="lineno">   8 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">LinearLayout</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">   9 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  10 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  11 </span>        <span class="prefix">android:</span><span class="attribute">layout_margin</span>=<span class="value">"20dp"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUselessParent" style="display: none;">
A layout with children that has no siblings, is not a scrollview or a root layout, and does not have a background, can be removed and have its children moved directly into the parent for a flatter and more efficient layout hierarchy.<br/>To suppress this error, use the issue id "UselessParent" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UselessParent</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 2/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUselessParentLink" onclick="reveal('explanationUselessParent');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UselessParentCardLink" onclick="hideid('UselessParentCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Usability:Icons"></a>
<a name="MonochromeLauncherIcon"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MonochromeLauncherIconCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Monochrome icon is not defined</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml">../../src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml</a>:2</span>: <span class="message">The application adaptive roundIcon is missing a monochrome tag</span><br /><pre class="errorlines">
<span class="lineno"> 1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno"> 2 </span><span class="warning"><span class="tag">&lt;adaptive-icon</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 3 </span>    <span class="tag">&lt;background</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">drawable</span>=<span class="value">"@drawable/ic_launcher_background"</span> />
<span class="lineno"> 4 </span>    <span class="tag">&lt;foreground</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">drawable</span>=<span class="value">"@drawable/ic_launcher_foreground"</span> />
<span class="lineno"> 5 </span><span class="tag">&lt;/adaptive-icon></span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationMonochromeLauncherIcon" style="display: none;">
If <code>android:roundIcon</code> and <code>android:icon</code> are both in your manifest, you must either remove the reference to <code>android:roundIcon</code> if it is not needed; or, supply the monochrome icon in the drawable defined by the <code>android:roundIcon</code> and <code>android:icon</code> attribute.<br/>
<br/>
For example, if <code>android:roundIcon</code> and <code>android:icon</code> are both in the manifest, a launcher might choose to use <code>android:roundIcon</code> over <code>android:icon</code> to display the adaptive app icon. Therefore, your themed application iconwill not show if your monochrome attribute is not also specified in <code>android:roundIcon</code>.<br/>To suppress this error, use the issue id "MonochromeLauncherIcon" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">MonochromeLauncherIcon</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Icons</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationMonochromeLauncherIconLink" onclick="reveal('explanationMonochromeLauncherIcon');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MonochromeLauncherIconCardLink" onclick="hideid('MonochromeLauncherIconCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="IconLocation"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="IconLocationCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Image defined in density-independent drawable folder</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/drawable/bill.jpg">../../src/main/res/drawable/bill.jpg</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/bill.jpg" /><span class="message">Found bitmap drawable <code>res/drawable/bill.jpg</code> in densityless folder</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/cookbear.jpg">../../src/main/res/drawable/cookbear.jpg</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/cookbear.jpg" /><span class="message">Found bitmap drawable <code>res/drawable/cookbear.jpg</code> in densityless folder</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/cookbear2.jpg">../../src/main/res/drawable/cookbear2.jpg</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/cookbear2.jpg" /><span class="message">Found bitmap drawable <code>res/drawable/cookbear2.jpg</code> in densityless folder</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/meat.jpg">../../src/main/res/drawable/meat.jpg</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/meat.jpg" /><span class="message">Found bitmap drawable <code>res/drawable/meat.jpg</code> in densityless folder</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/seafood.jpg">../../src/main/res/drawable/seafood.jpg</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/seafood.jpg" /><span class="message">Found bitmap drawable <code>res/drawable/seafood.jpg</code> in densityless folder</span><br clear="right"/>
<button class="mdl-button mdl-js-button mdl-button--primary" id="IconLocationDivLink" onclick="reveal('IconLocationDiv');" />+ 5 More Occurrences...</button>
<div id="IconLocationDiv" style="display: none">
<span class="location"><a href="../../src/main/res/drawable/speech_bubble.png">../../src/main/res/drawable/speech_bubble.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/speech_bubble.png" /><span class="message">Found bitmap drawable <code>res/drawable/speech_bubble.png</code> in densityless folder</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/takepic.png">../../src/main/res/drawable/takepic.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/takepic.png" /><span class="message">Found bitmap drawable <code>res/drawable/takepic.png</code> in densityless folder</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/vege1.jpg">../../src/main/res/drawable/vege1.jpg</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/vege1.jpg" /><span class="message">Found bitmap drawable <code>res/drawable/vege1.jpg</code> in densityless folder</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/vegetable.jpg">../../src/main/res/drawable/vegetable.jpg</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/vegetable.jpg" /><span class="message">Found bitmap drawable <code>res/drawable/vegetable.jpg</code> in densityless folder</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/welcom.jpg">../../src/main/res/drawable/welcom.jpg</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/welcom.jpg" /><span class="message">Found bitmap drawable <code>res/drawable/welcom.jpg</code> in densityless folder</span><br clear="right"/>
</div>
</div>
<div class="metadata"><div class="explanation" id="explanationIconLocation" style="display: none;">
The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to <code>drawable-mdpi</code> and consider providing higher and lower resolution versions in <code>drawable-ldpi</code>, <code>drawable-hdpi</code> and <code>drawable-xhdpi</code>. If the icon <b>really</b> is density independent (for example a solid color) you can place it in <code>drawable-nodpi</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/practices/screens_support.html">https://developer.android.com/guide/practices/screens_support.html</a>
</div>To suppress this error, use the issue id "IconLocation" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">IconLocation</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Icons</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationIconLocationLink" onclick="reveal('explanationIconLocation');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IconLocationCardLink" onclick="hideid('IconLocationCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Usability"></a>
<a name="ButtonStyle"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ButtonStyleCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Button should be borderless</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:22</span>: <span class="message">Buttons in button bars should be borderless; use <code>style="?android:attr/buttonBarButtonStyle"</code> (and <code>?android:attr/buttonBarStyle</code> on the parent)</span><br /><pre class="errorlines">
<span class="lineno">  19 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>
<span class="lineno">  20 </span>            <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/outside"</span>>
<span class="lineno">  21 </span>
<span class="caretline"><span class="lineno">  22 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  23 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/button5"</span>
<span class="lineno">  24 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  25 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:29</span>: <span class="message">Buttons in button bars should be borderless; use <code>style="?android:attr/buttonBarButtonStyle"</code> (and <code>?android:attr/buttonBarStyle</code> on the parent)</span><br /><pre class="errorlines">
<span class="lineno">  26 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno">  27 </span>                <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"首頁"</span> />
<span class="lineno">  28 </span>
<span class="caretline"><span class="lineno">  29 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  30 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/button4"</span>
<span class="lineno">  31 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  32 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:36</span>: <span class="message">Buttons in button bars should be borderless; use <code>style="?android:attr/buttonBarButtonStyle"</code> (and <code>?android:attr/buttonBarStyle</code> on the parent)</span><br /><pre class="errorlines">
<span class="lineno">  33 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno">  34 </span>                <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"總清單"</span> />
<span class="lineno">  35 </span>
<span class="caretline"><span class="lineno">  36 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  37 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/button3"</span>
<span class="lineno">  38 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  39 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:43</span>: <span class="message">Buttons in button bars should be borderless; use <code>style="?android:attr/buttonBarButtonStyle"</code> (and <code>?android:attr/buttonBarStyle</code> on the parent)</span><br /><pre class="errorlines">
<span class="lineno">  40 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno">  41 </span>                <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"菜價分析"</span> />
<span class="lineno">  42 </span>
<span class="caretline"><span class="lineno">  43 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  44 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/button"</span>
<span class="lineno">  45 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  46 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:331</span>: <span class="message">Buttons in button bars should be borderless; use <code>style="?android:attr/buttonBarButtonStyle"</code> (and <code>?android:attr/buttonBarStyle</code> on the parent)</span><br /><pre class="errorlines">
<span class="lineno"> 328 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 329 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno"> 330 </span>
<span class="caretline"><span class="lineno"> 331 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 332 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/positivePopupBtn"</span>
<span class="lineno"> 333 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 334 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:338</span>: <span class="message">Buttons in button bars should be borderless; use <code>style="?android:attr/buttonBarButtonStyle"</code> (and <code>?android:attr/buttonBarStyle</code> on the parent)</span><br /><pre class="errorlines">
<span class="lineno"> 335 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 336 </span>                <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"儲存"</span> />
<span class="lineno"> 337 </span>
<span class="caretline"><span class="lineno"> 338 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 339 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/negativePopupBtn"</span>
<span class="lineno"> 340 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 341 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationButtonStyle" style="display: none;">
Button bars typically use a borderless style for the buttons. Set the <code>style="?android:attr/buttonBarButtonStyle"</code> attribute on each of the buttons, and set <code>style="?android:attr/buttonBarStyle"</code> on the parent layout<br/><div class="moreinfo">More info: <a href="https://material.io/components/dialogs/">https://material.io/components/dialogs/</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ButtonStyle" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ButtonStyle</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationButtonStyleLink" onclick="reveal('explanationButtonStyle');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ButtonStyleCardLink" onclick="hideid('ButtonStyleCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="Autofill"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="AutofillCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use Autofill</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:313</span>: <span class="message">Missing <code>autofillHints</code> attribute</span><br /><pre class="errorlines">
<span class="lineno"> 310 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span>
<span class="lineno"> 311 </span>                <span class="prefix">tools:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span> />
<span class="lineno"> 312 </span>
<span class="caretline"><span class="lineno"> 313 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 314 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/etRemarks"</span>
<span class="lineno"> 315 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 316 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/disc2.xml">../../src/main/res/layout/disc2.xml</a>:24</span>: <span class="message">Missing <code>autofillHints</code> attribute</span><br /><pre class="errorlines">
<span class="lineno"> 21 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintStart_toStartOf</span>=<span class="value">"parent"</span>
<span class="lineno"> 22 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintTop_toTopOf</span>=<span class="value">"parent"</span> />
<span class="lineno"> 23 </span>
<span class="caretline"><span class="lineno"> 24 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 25 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/message"</span>
<span class="lineno"> 26 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno"> 27 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_favorites.xml">../../src/main/res/layout/fragment_favorites.xml</a>:22</span>: <span class="message">Missing <code>autofillHints</code> attribute</span><br /><pre class="errorlines">
<span class="lineno"> 19 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintStart_toStartOf</span>=<span class="value">"parent"</span>
<span class="lineno"> 20 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintTop_toTopOf</span>=<span class="value">"parent"</span> />
<span class="lineno"> 21 </span>
<span class="caretline"><span class="lineno"> 22 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 23 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/nickname"</span>
<span class="lineno"> 24 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 25 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_search.xml">../../src/main/res/layout/fragment_search.xml</a>:95</span>: <span class="message">Missing <code>autofillHints</code> attribute</span><br /><pre class="errorlines">
<span class="lineno">  92 </span>            <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center"</span>
<span class="lineno">  93 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno">  94 </span>
<span class="caretline"><span class="lineno">  95 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  96 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/editText"</span>
<span class="lineno">  97 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  98 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_search.xml">../../src/main/res/layout/fragment_search.xml</a>:117</span>: <span class="message">Missing <code>autofillHints</code> attribute</span><br /><pre class="errorlines">
<span class="lineno"> 114 </span>            <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center"</span>
<span class="lineno"> 115 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno"> 116 </span>
<span class="caretline"><span class="lineno"> 117 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 118 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/editText1"</span>
<span class="lineno"> 119 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 120 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationAutofill" style="display: none;">
Specify an <code>autofillHints</code> attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.<br/>
<br/>
The hints can have any value, but it is recommended to use predefined values like 'username' for a username or 'creditCardNumber' for a credit card number. For a list of all predefined autofill hint constants, see the <code>AUTOFILL_HINT_</code> constants in the <code>View</code> reference at <a href="https://developer.android.com/reference/android/view/View.html">https://developer.android.com/reference/android/view/View.html</a>.<br/>
<br/>
You can mark a view unimportant for autofill by specifying an <code>importantForAutofill</code> attribute on that view or a parent view. See <a href="https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)">https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)</a>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/text/autofill.html">https://developer.android.com/guide/topics/text/autofill.html</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "Autofill" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">Autofill</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationAutofillLink" onclick="reveal('explanationAutofill');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="AutofillCardLink" onclick="hideid('AutofillCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Accessibility"></a>
<a name="ContentDescription"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ContentDescriptionCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Image without contentDescription</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:78</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  75 </span>            <span class="prefix">app:</span><span class="attribute">layout_constraintStart_toStartOf</span>=<span class="value">"parent"</span>
<span class="lineno">  76 </span>            <span class="prefix">tools:</span><span class="attribute">layout_editor_absoluteY</span>=<span class="value">"204dp"</span>>
<span class="lineno">  77 </span>
<span class="caretline"><span class="lineno">  78 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  79 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/imv"</span>
<span class="lineno">  80 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"80dp"</span>
<span class="lineno">  81 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"59dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:115</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 112 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno"> 113 </span>
<span class="lineno"> 114 </span>
<span class="caretline"><span class="lineno"> 115 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 116 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/imv2"</span>
<span class="lineno"> 117 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"60dp"</span>
<span class="lineno"> 118 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buymoney.xml">../../src/main/res/layout/activity_buymoney.xml</a>:42</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 39 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 40 </span>                <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"價格低至高"</span> />
<span class="lineno"> 41 </span>
<span class="caretline"><span class="lineno"> 42 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 43 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/imageView3"</span>
<span class="lineno"> 44 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 45 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_vganalysis.xml">../../src/main/res/layout/activity_vganalysis.xml</a>:42</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 39 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 40 </span>                <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"已到期"</span> />
<span class="lineno"> 41 </span>
<span class="caretline"><span class="lineno"> 42 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageButton</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 43 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/imageButton"</span>
<span class="lineno"> 44 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 45 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_welcome.xml">../../src/main/res/layout/activity_welcome.xml</a>:9</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno">  7 </span>    <span class="prefix">tools:</span><span class="attribute">context</span>=<span class="value">".WelcomeActivty"</span>>
<span class="lineno">  8 </span>
<span class="caretline"><span class="lineno">  9 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 10 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/welcome"</span>
<span class="lineno"> 11 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 12 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"0dp"</span>
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="ContentDescriptionDivLink" onclick="reveal('ContentDescriptionDiv');" />+ 13 More Occurrences...</button>
<div id="ContentDescriptionDiv" style="display: none">
<span class="location"><a href="../../src/main/res/layout/epic_popup_negtive.xml">../../src/main/res/layout/epic_popup_negtive.xml</a>:18</span>: <span class="message">Empty <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  15 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span>=<span class="value">"true"</span>
<span class="lineno">  16 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"7dp"</span>
<span class="lineno">  17 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"7dp"</span>
<span class="caretline"><span class="lineno">  18 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">contentDescription</span>=<span class="value">"TODO"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  19 </span>        <span class="prefix">android:</span><span class="attribute">elevation</span>=<span class="value">"5dp"</span>
<span class="lineno">  20 </span>        <span class="prefix">app:</span><span class="attribute">srcCompat</span>=<span class="value">"@drawable/ic_close_black_24dp"</span> />
<span class="lineno">  21 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_negtive.xml">../../src/main/res/layout/epic_popup_negtive.xml</a>:62</span>: <span class="message">Empty <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  59 </span>                    <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"20dp"</span>
<span class="lineno">  60 </span>                    <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"10dp"</span>
<span class="lineno">  61 </span>                    <span class="prefix">app:</span><span class="attribute">srcCompat</span>=<span class="value">"@drawable/ic_error_outline_black_24dp"</span>
<span class="caretline"><span class="lineno">  62 </span>                    <span class="warning"><span class="prefix">android:</span><span class="attribute">contentDescription</span>=<span class="value">"TODO"</span></span>><span class="tag">&lt;/ImageView></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  63 </span>
<span class="lineno">  64 </span>                <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno">  65 </span><span class="attribute">                    </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/messageTv"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_positive.xml">../../src/main/res/layout/epic_popup_positive.xml</a>:19</span>: <span class="message">Empty <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  16 </span>        <span class="prefix">android:</span><span class="attribute">elevation</span>=<span class="value">"5dp"</span>
<span class="lineno">  17 </span>        <span class="prefix">app:</span><span class="attribute">srcCompat</span>=<span class="value">"@drawable/ic_close_black_24dp"</span>
<span class="lineno">  18 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno">  19 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">contentDescription</span>=<span class="value">"TODO"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  20 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"7dp"</span> />
<span class="lineno">  21 </span>
<span class="lineno">  22 </span>    <span class="tag">&lt;androidx.cardview.widget.CardView</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_positive.xml">../../src/main/res/layout/epic_popup_positive.xml</a>:54</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  51 </span>                    <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
<span class="lineno">  52 </span>                    <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"@string/positive_popup_title_default"</span>/>
<span class="lineno">  53 </span>
<span class="caretline"><span class="lineno">  54 </span>                <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  55 </span><span class="attribute">                    </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  56 </span>                    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  57 </span>                    <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"20dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/forum_listitem2.xml">../../src/main/res/layout/forum_listitem2.xml</a>:15</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 12 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 13 </span>        <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno"> 14 </span>
<span class="caretline"><span class="lineno"> 15 </span>        <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 16 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/imageView2"</span>
<span class="lineno"> 17 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"201dp"</span>
<span class="lineno"> 18 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"143dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_favorites.xml">../../src/main/res/layout/fragment_favorites.xml</a>:38</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 35 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintStart_toStartOf</span>=<span class="value">"parent"</span>
<span class="lineno"> 36 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintTop_toBottomOf</span>=<span class="value">"@+id/textView"</span> />
<span class="lineno"> 37 </span>
<span class="caretline"><span class="lineno"> 38 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 39 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/imageview"</span>
<span class="lineno"> 40 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"298dp"</span>
<span class="lineno"> 41 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"198dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_search.xml">../../src/main/res/layout/fragment_search.xml</a>:27</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  24 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  25 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno">  26 </span>
<span class="caretline"><span class="lineno">  27 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  28 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/imageView4"</span>
<span class="lineno">  29 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"20dp"</span>
<span class="lineno">  30 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_search.xml">../../src/main/res/layout/fragment_search.xml</a>:49</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  46 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  47 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno">  48 </span>
<span class="caretline"><span class="lineno">  49 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  50 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/imageVie4"</span>
<span class="lineno">  51 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"20dp"</span>
<span class="lineno">  52 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_search.xml">../../src/main/res/layout/fragment_search.xml</a>:71</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  68 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  69 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno">  70 </span>
<span class="caretline"><span class="lineno">  71 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  72 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/imageie4"</span>
<span class="lineno">  73 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"20dp"</span>
<span class="lineno">  74 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_search.xml">../../src/main/res/layout/fragment_search.xml</a>:104</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 101 </span>                <span class="prefix">android:</span><span class="attribute">inputType</span>=<span class="value">"textPersonName"</span>
<span class="lineno"> 102 </span>                <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"請輸入信箱"</span> />
<span class="lineno"> 103 </span>
<span class="caretline"><span class="lineno"> 104 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 105 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/imageView"</span>
<span class="lineno"> 106 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"40dp"</span>
<span class="lineno"> 107 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_search.xml">../../src/main/res/layout/fragment_search.xml</a>:126</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 123 </span>                <span class="prefix">android:</span><span class="attribute">inputType</span>=<span class="value">"textPersonName"</span>
<span class="lineno"> 124 </span>                <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"請輸入電話"</span> />
<span class="lineno"> 125 </span>
<span class="caretline"><span class="lineno"> 126 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 127 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/imageView1"</span>
<span class="lineno"> 128 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"40dp"</span>
<span class="lineno"> 129 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/item.xml">../../src/main/res/layout/item.xml</a>:11</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  8 </span>    <span class="tag">&lt;RelativeLayout</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
<span class="lineno">  9 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"fill_parent"</span>
<span class="lineno"> 10 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"?android:attr/listPreferredItemHeight"</span>>
<span class="caretline"><span class="lineno"> 11 </span>        <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/photo"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 12 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"80dip"</span>
<span class="lineno"> 13 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"20dip"</span>
<span class="lineno"> 14 </span>            <span class="prefix">android:</span><span class="attribute">layout_alignParentTop</span>=<span class="value">"true"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/item1.xml">../../src/main/res/layout/item1.xml</a>:25</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  22 </span>            <span class="prefix">tools:</span><span class="attribute">text</span>=<span class="value">"ID"</span> />
<span class="lineno">  23 </span>    <span class="tag">&lt;/LinearLayout></span>
<span class="lineno">  24 </span>
<span class="caretline"><span class="lineno">  25 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  26 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/photo"</span>
<span class="lineno">  27 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"300dp"</span>
<span class="lineno">  28 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"150dp"</span>
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationContentDescription" style="display: none;">
Non-textual widgets like ImageViews and ImageButtons should use the <code>contentDescription</code> attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.<br/>
<br/>
Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to <code>@null</code>. If your app's minSdkVersion is 16 or higher, you can instead set these graphical elements' <code>android:importantForAccessibility</code> attributes to <code>no</code>.<br/>
<br/>
Note that for text fields, you should not set both the <code>hint</code> and the <code>contentDescription</code> attributes since the hint will never be shown. Just set the <code>hint</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases">https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ContentDescription" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ContentDescription</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Accessibility</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationContentDescriptionLink" onclick="reveal('explanationContentDescription');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ContentDescriptionCardLink" onclick="hideid('ContentDescriptionCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="LabelFor"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="LabelForCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Missing accessibility label</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:313</span>: <span class="message">Missing accessibility label: provide either a view with an <code>android:labelFor</code> that references this view or provide an <code>android:hint</code></span><br /><pre class="errorlines">
<span class="lineno"> 310 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span>
<span class="lineno"> 311 </span>                <span class="prefix">tools:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span> />
<span class="lineno"> 312 </span>
<span class="caretline"><span class="lineno"> 313 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 314 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/etRemarks"</span>
<span class="lineno"> 315 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 316 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_favorites.xml">../../src/main/res/layout/fragment_favorites.xml</a>:22</span>: <span class="message">Missing accessibility label: provide either a view with an <code>android:labelFor</code> that references this view or provide an <code>android:hint</code></span><br /><pre class="errorlines">
<span class="lineno"> 19 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintStart_toStartOf</span>=<span class="value">"parent"</span>
<span class="lineno"> 20 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintTop_toTopOf</span>=<span class="value">"parent"</span> />
<span class="lineno"> 21 </span>
<span class="caretline"><span class="lineno"> 22 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 23 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/nickname"</span>
<span class="lineno"> 24 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 25 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_search.xml">../../src/main/res/layout/fragment_search.xml</a>:95</span>: <span class="message">Missing accessibility label: provide either a view with an <code>android:labelFor</code> that references this view or provide an <code>android:hint</code></span><br /><pre class="errorlines">
<span class="lineno">  92 </span>            <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center"</span>
<span class="lineno">  93 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno">  94 </span>
<span class="caretline"><span class="lineno">  95 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  96 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/editText"</span>
<span class="lineno">  97 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  98 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_search.xml">../../src/main/res/layout/fragment_search.xml</a>:117</span>: <span class="message">Missing accessibility label: provide either a view with an <code>android:labelFor</code> that references this view or provide an <code>android:hint</code></span><br /><pre class="errorlines">
<span class="lineno"> 114 </span>            <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center"</span>
<span class="lineno"> 115 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno"> 116 </span>
<span class="caretline"><span class="lineno"> 117 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 118 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/editText1"</span>
<span class="lineno"> 119 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 120 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationLabelFor" style="display: none;">
Editable text fields should provide an <code>android:hint</code> or, provided your <code>minSdkVersion</code> is at least 17, they may be referenced by a view with a <code>android:labelFor</code> attribute.<br/>
<br/>
When using <code>android:labelFor</code>, be sure to provide an <code>android:text</code> or an <code>android:contentDescription</code>.<br/>
<br/>
If your view is labeled but by a label in a different layout which includes this one, just suppress this warning from lint.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "LabelFor" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">LabelFor</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Accessibility</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 2/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationLabelForLink" onclick="reveal('explanationLabelFor');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="LabelForCardLink" onclick="hideid('LabelForCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Internationalization"></a>
<a name="SetTextI18n"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SetTextI18nCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">TextView Internationalization</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/BuyActivity.java">../../src/main/java/com/example/bottomnavigationview/BuyActivity.java</a>:128</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 125 </span>  buydate = (TextView) findViewById(R.id.buydate);<span class="comment">//日期用</span>
<span class="lineno"> 126 </span>  savedate=(TextView)findViewById(R.id.savedate);
<span class="lineno"> 127 </span>  buydate.setOnClickListener(<span class="keyword">this</span>);<span class="comment">//日期用</span>
<span class="caretline"><span class="lineno"> 128 </span>  buydate.setText(<span class="warning">Integer.toString(thisYear)+<span class="string">"/"</span> + Integer.toString(thisMonth)+<span class="string">"/"</span> + Integer.toString(thisDate)</span>);<span class="comment">//日期用</span></span>
<span class="lineno"> 129 </span><span class="lineno"> 130 </span></pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/BuyActivity.java">../../src/main/java/com/example/bottomnavigationview/BuyActivity.java</a>:128</span>: <span class="message">Number formatting does not take into account locale settings. Consider using <code>String.format</code> instead.</span><br /><pre class="errorlines">
<span class="lineno"> 125 </span>  buydate = (TextView) findViewById(R.id.buydate);<span class="comment">//日期用</span>
<span class="lineno"> 126 </span>  savedate=(TextView)findViewById(R.id.savedate);
<span class="lineno"> 127 </span>  buydate.setOnClickListener(<span class="keyword">this</span>);<span class="comment">//日期用</span>
<span class="caretline"><span class="lineno"> 128 </span>  buydate.setText(<span class="warning">Integer.toString(thisYear)</span>+<span class="string">"/"</span> + Integer.toString(thisMonth)+<span class="string">"/"</span> + Integer.toString(thisDate));<span class="comment">//日期用</span></span>
<span class="lineno"> 129 </span><span class="lineno"> 130 </span></pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/BuyActivity.java">../../src/main/java/com/example/bottomnavigationview/BuyActivity.java</a>:128</span>: <span class="message">Number formatting does not take into account locale settings. Consider using <code>String.format</code> instead.</span><br /><pre class="errorlines">
<span class="lineno"> 125 </span>  buydate = (TextView) findViewById(R.id.buydate);<span class="comment">//日期用</span>
<span class="lineno"> 126 </span>  savedate=(TextView)findViewById(R.id.savedate);
<span class="lineno"> 127 </span>  buydate.setOnClickListener(<span class="keyword">this</span>);<span class="comment">//日期用</span>
<span class="caretline"><span class="lineno"> 128 </span>  buydate.setText(Integer.toString(thisYear)+<span class="string">"/"</span> + <span class="warning">Integer.toString(thisMonth)</span>+<span class="string">"/"</span> + Integer.toString(thisDate));<span class="comment">//日期用</span></span>
<span class="lineno"> 129 </span><span class="lineno"> 130 </span></pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/BuyActivity.java">../../src/main/java/com/example/bottomnavigationview/BuyActivity.java</a>:128</span>: <span class="message">Number formatting does not take into account locale settings. Consider using <code>String.format</code> instead.</span><br /><pre class="errorlines">
<span class="lineno"> 125 </span>  buydate = (TextView) findViewById(R.id.buydate);<span class="comment">//日期用</span>
<span class="lineno"> 126 </span>  savedate=(TextView)findViewById(R.id.savedate);
<span class="lineno"> 127 </span>  buydate.setOnClickListener(<span class="keyword">this</span>);<span class="comment">//日期用</span>
<span class="caretline"><span class="lineno"> 128 </span>  buydate.setText(Integer.toString(thisYear)+<span class="string">"/"</span> + Integer.toString(thisMonth)+<span class="string">"/"</span> + <span class="warning">Integer.toString(thisDate)</span>);<span class="comment">//日期用</span></span>
<span class="lineno"> 129 </span><span class="lineno"> 130 </span></pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/BuyActivity.java">../../src/main/java/com/example/bottomnavigationview/BuyActivity.java</a>:557</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 554 </span>
<span class="lineno"> 555 </span>        <span class="annotation">@Override</span>
<span class="lineno"> 556 </span>    <span class="keyword">public</span> <span class="keyword">void</span> onDateSet(DatePicker view, <span class="keyword">int</span> year, <span class="keyword">int</span> month, <span class="keyword">int</span> dayOfMonth) {
<span class="caretline"><span class="lineno"> 557 </span>            buydate.setText(( <span class="warning">year + <span class="string">"/"</span> + (month + <span class="number">1</span>) + <span class="string">"/"</span> + dayOfMonth</span>));&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 558 </span>    }
<span class="lineno"> 559 </span>
<span class="lineno"> 560 </span>    <span class="annotation">@Override</span></pre>

<span class="location"><a href="../../src/main/java/com/example/bottomnavigationview/BuyActivity.java">../../src/main/java/com/example/bottomnavigationview/BuyActivity.java</a>:557</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 554 </span>
<span class="lineno"> 555 </span>        <span class="annotation">@Override</span>
<span class="lineno"> 556 </span>    <span class="keyword">public</span> <span class="keyword">void</span> onDateSet(DatePicker view, <span class="keyword">int</span> year, <span class="keyword">int</span> month, <span class="keyword">int</span> dayOfMonth) {
<span class="caretline"><span class="lineno"> 557 </span>            buydate.setText(( year + <span class="string">"/"</span> + (<span class="warning">month + <span class="number">1</span></span>) + <span class="string">"/"</span> + dayOfMonth));&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 558 </span>    }
<span class="lineno"> 559 </span>
<span class="lineno"> 560 </span>    <span class="annotation">@Override</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationSetTextI18n" style="display: none;">
When calling <code>TextView#setText</code><br/>
* Never call <code>Number#toString()</code> to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using <code>String#format</code> with proper format specifications (<code>%d</code> or <code>%f</code>) instead.<br/>
* Do not pass a string literal (e.g. "Hello") to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.<br/>
* Do not build messages by concatenating text chunks. Such messages can not be properly translated.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/resources/localization.html">https://developer.android.com/guide/topics/resources/localization.html</a>
</div>To suppress this error, use the issue id "SetTextI18n" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">SetTextI18n</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationSetTextI18nLink" onclick="reveal('explanationSetTextI18n');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SetTextI18nCardLink" onclick="hideid('SetTextI18nCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="HardcodedText"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="HardcodedTextCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Hardcoded text</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:27</span>: <span class="message">Hardcoded string "&#39318;&#38913;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  24 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  25 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  26 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno">  27 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"首頁"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  28 </span>
<span class="lineno">  29 </span>            <span class="tag">&lt;Button</span><span class="attribute">
</span><span class="lineno">  30 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/button4"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:34</span>: <span class="message">Hardcoded string "&#32317;&#28165;&#21934;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  31 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  32 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  33 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno">  34 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"總清單"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  35 </span>
<span class="lineno">  36 </span>            <span class="tag">&lt;Button</span><span class="attribute">
</span><span class="lineno">  37 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/button3"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:41</span>: <span class="message">Hardcoded string "&#33756;&#20729;&#20998;&#26512;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  38 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  39 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  40 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno">  41 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"菜價分析"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  42 </span>
<span class="lineno">  43 </span>            <span class="tag">&lt;Button</span><span class="attribute">
</span><span class="lineno">  44 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/button"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:48</span>: <span class="message">Hardcoded string "&#20445;&#23384;&#26399;&#38480;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  45 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  46 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  47 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno">  48 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"保存期限"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  49 </span>        <span class="tag">&lt;/LinearLayout></span>
<span class="lineno">  50 </span>
<span class="lineno">  51 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:93</span>: <span class="message">Hardcoded string "&#36092;&#36023;&#26085;&#26399;:", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  90 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"140dp"</span>
<span class="lineno">  91 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno">  92 </span>                <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center"</span>
<span class="caretline"><span class="lineno">  93 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"購買日期:"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  94 </span>                <span class="prefix">tools:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span> />
<span class="lineno">  95 </span>
<span class="lineno">  96 </span>            <span class="tag">&lt;TextView</span><span class="attribute">
</span></pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="HardcodedTextDivLink" onclick="reveal('HardcodedTextDiv');" />+ 79 More Occurrences...</button>
<div id="HardcodedTextDiv" style="display: none">
<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:102</span>: <span class="message">Hardcoded string "2019-10-08", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  99 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 100 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 101 </span>                <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center|center_horizontal|center_vertical"</span>
<span class="caretline"><span class="lineno"> 102 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"2019-10-08"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 103 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span>
<span class="lineno"> 104 </span>                <span class="prefix">tools:</span><span class="attribute">text</span>=<span class="value">"@tools:sample/date/mmddyy"</span> />
<span class="lineno"> 105 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:128</span>: <span class="message">Hardcoded string "&#20445;&#23384;&#26399;&#26399;:", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 125 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"140dp"</span>
<span class="lineno"> 126 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 127 </span>                <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center"</span>
<span class="caretline"><span class="lineno"> 128 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"保存期期:"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 129 </span>                <span class="prefix">tools:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span> />
<span class="lineno"> 130 </span>
<span class="lineno"> 131 </span>            <span class="tag">&lt;TextView</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:137</span>: <span class="message">Hardcoded string "2019-10-15", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 134 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 135 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 136 </span>                <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center|center_horizontal|center_vertical"</span>
<span class="caretline"><span class="lineno"> 137 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"2019-10-15"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 138 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span>
<span class="lineno"> 139 </span>                <span class="prefix">tools:</span><span class="attribute">text</span>=<span class="value">"@tools:sample/date/mmddyy"</span> />
<span class="lineno"> 140 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:154</span>: <span class="message">Hardcoded string "&#31278;&#39006;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 151 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"163dp"</span>
<span class="lineno"> 152 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 153 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno"> 154 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"種類"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 155 </span>                <span class="prefix">android:</span><span class="attribute">textAlignment</span>=<span class="value">"center"</span>
<span class="lineno"> 156 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span> />
<span class="lineno"> 157 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:185</span>: <span class="message">Hardcoded string "&#23376;&#39006;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 182 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"163dp"</span>
<span class="lineno"> 183 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 184 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno"> 185 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"子類"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 186 </span>                <span class="prefix">android:</span><span class="attribute">textAlignment</span>=<span class="value">"center"</span>
<span class="lineno"> 187 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span> />
<span class="lineno"> 188 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:216</span>: <span class="message">Hardcoded string "&#37096;&#20301;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 213 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"163dp"</span>
<span class="lineno"> 214 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 215 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno"> 216 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"部位"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 217 </span>                <span class="prefix">android:</span><span class="attribute">textAlignment</span>=<span class="value">"center"</span>
<span class="lineno"> 218 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span> />
<span class="lineno"> 219 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:245</span>: <span class="message">Hardcoded string "&#20221;&#25976;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 242 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"163dp"</span>
<span class="lineno"> 243 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 244 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno"> 245 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"份數"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 246 </span>                <span class="prefix">android:</span><span class="attribute">textAlignment</span>=<span class="value">"center"</span>
<span class="lineno"> 247 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span> />
<span class="lineno"> 248 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:276</span>: <span class="message">Hardcoded string "&#20301;&#32622;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 273 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"163dp"</span>
<span class="lineno"> 274 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 275 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno"> 276 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"位置"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 277 </span>                <span class="prefix">android:</span><span class="attribute">textAlignment</span>=<span class="value">"center"</span>
<span class="lineno"> 278 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span> />
<span class="lineno"> 279 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:309</span>: <span class="message">Hardcoded string "&#20633;&#35387;:", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 306 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 307 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"0.2"</span>
<span class="lineno"> 308 </span>                <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center|center_horizontal"</span>
<span class="caretline"><span class="lineno"> 309 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"備註:"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 310 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span>
<span class="lineno"> 311 </span>                <span class="prefix">tools:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span> />
<span class="lineno"> 312 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:336</span>: <span class="message">Hardcoded string "&#20786;&#23384;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 333 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 334 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 335 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno"> 336 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"儲存"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 337 </span>
<span class="lineno"> 338 </span>            <span class="tag">&lt;Button</span><span class="attribute">
</span><span class="lineno"> 339 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/negativePopupBtn"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:343</span>: <span class="message">Hardcoded string "&#20877;&#35352;&#19968;&#31558;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 340 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 341 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 342 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno"> 343 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"再記一筆"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 344 </span>        <span class="tag">&lt;/LinearLayout></span>
<span class="lineno"> 345 </span>
<span class="lineno"> 346 </span>        <span class="tag">&lt;Button</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:351</span>: <span class="message">Hardcoded string "Button", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 348 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 349 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 350 </span>            <span class="prefix">android:</span><span class="attribute">onClick</span>=<span class="value">"btSure"</span>
<span class="caretline"><span class="lineno"> 351 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Button"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 352 </span>
<span class="lineno"> 353 </span>
<span class="lineno"> 354 </span>    <span class="tag">&lt;/LinearLayout></span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buymoney.xml">../../src/main/res/layout/activity_buymoney.xml</a>:20</span>: <span class="message">Hardcoded string "&#27511;&#21490;&#33756;&#20729;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 17 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 18 </span>            <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"#8F7DAD"</span>
<span class="lineno"> 19 </span>            <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center"</span>
<span class="caretline"><span class="lineno"> 20 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"歷史菜價"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 21 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"36sp"</span> />
<span class="lineno"> 22 </span>
<span class="lineno"> 23 </span>        <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_buymoney.xml">../../src/main/res/layout/activity_buymoney.xml</a>:33</span>: <span class="message">Hardcoded string "&#20729;&#26684;&#39640;&#33267;&#20302;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 30 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 31 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 32 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno"> 33 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"價格高至低"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 34 </span>
<span class="lineno"> 35 </span>            <span class="tag">&lt;Button</span><span class="attribute">
</span><span class="lineno"> 36 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/button4"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buymoney.xml">../../src/main/res/layout/activity_buymoney.xml</a>:40</span>: <span class="message">Hardcoded string "&#20729;&#26684;&#20302;&#33267;&#39640;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 37 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 38 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 39 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno"> 40 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"價格低至高"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 41 </span>
<span class="lineno"> 42 </span>            <span class="tag">&lt;ImageView</span><span class="attribute">
</span><span class="lineno"> 43 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/imageView3"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_pre_vegetables.xml">../../src/main/res/layout/activity_pre_vegetables.xml</a>:20</span>: <span class="message">Hardcoded string "&#32317;&#28165;&#21934;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 17 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 18 </span>            <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"#FFEB3B"</span>
<span class="lineno"> 19 </span>            <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center"</span>
<span class="caretline"><span class="lineno"> 20 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"總清單"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 21 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"36sp"</span> />
<span class="lineno"> 22 </span>
<span class="lineno"> 23 </span>        <span class="tag">&lt;ListView</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_vganalysis.xml">../../src/main/res/layout/activity_vganalysis.xml</a>:20</span>: <span class="message">Hardcoded string "&#20445;&#23384;&#25552;&#37266;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 17 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 18 </span>            <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"#CDDC39"</span>
<span class="lineno"> 19 </span>            <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center"</span>
<span class="caretline"><span class="lineno"> 20 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"保存提醒"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 21 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"36sp"</span> />
<span class="lineno"> 22 </span>
<span class="lineno"> 23 </span>        <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_vganalysis.xml">../../src/main/res/layout/activity_vganalysis.xml</a>:33</span>: <span class="message">Hardcoded string "&#24555;&#21040;&#26399;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 30 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 31 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 32 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno"> 33 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"快到期"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 34 </span>
<span class="lineno"> 35 </span>            <span class="tag">&lt;Button</span><span class="attribute">
</span><span class="lineno"> 36 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/button6"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_vganalysis.xml">../../src/main/res/layout/activity_vganalysis.xml</a>:40</span>: <span class="message">Hardcoded string "&#24050;&#21040;&#26399;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 37 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 38 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 39 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno"> 40 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"已到期"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 41 </span>
<span class="lineno"> 42 </span>            <span class="tag">&lt;ImageButton</span><span class="attribute">
</span><span class="lineno"> 43 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/imageButton"</span>
</pre>

<span class="location"><a href="../../src/main/res/menu/bottom_navigation.xml">../../src/main/res/menu/bottom_navigation.xml</a>:6</span>: <span class="message">Hardcoded string "&#20027;&#38913;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  3 </span>    <span class="tag">&lt;item</span><span class="attribute">
</span><span class="lineno">  4 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/nav_home"</span>
<span class="lineno">  5 </span>        <span class="prefix">android:</span><span class="attribute">icon</span>=<span class="value">"@drawable/ic_home_black_24dp"</span>
<span class="caretline"><span class="lineno">  6 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">title</span>=<span class="value">"主頁"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  7 </span>    <span class="tag">&lt;item</span><span class="attribute">
</span><span class="lineno">  8 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/nav_favorite"</span>
<span class="lineno">  9 </span>        <span class="prefix">android:</span><span class="attribute">icon</span>=<span class="value">"@android:drawable/ic_search_category_default"</span>
</pre>

<span class="location"><a href="../../src/main/res/menu/bottom_navigation.xml">../../src/main/res/menu/bottom_navigation.xml</a>:10</span>: <span class="message">Hardcoded string "&#25512;&#34214;&#39135;&#35676;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  7 </span>    <span class="tag">&lt;item</span><span class="attribute">
</span><span class="lineno">  8 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/nav_favorite"</span>
<span class="lineno">  9 </span>        <span class="prefix">android:</span><span class="attribute">icon</span>=<span class="value">"@android:drawable/ic_search_category_default"</span>
<span class="caretline"><span class="lineno"> 10 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">title</span>=<span class="value">"推薦食譜"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 11 </span>    <span class="tag">&lt;item</span><span class="attribute">
</span><span class="lineno"> 12 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/nav_search"</span>
<span class="lineno"> 13 </span>        <span class="prefix">android:</span><span class="attribute">icon</span>=<span class="value">"@android:drawable/star_big_on"</span>
</pre>

<span class="location"><a href="../../src/main/res/menu/bottom_navigation.xml">../../src/main/res/menu/bottom_navigation.xml</a>:14</span>: <span class="message">Hardcoded string "&#20491;&#20154;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 11 </span>    <span class="tag">&lt;item</span><span class="attribute">
</span><span class="lineno"> 12 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/nav_search"</span>
<span class="lineno"> 13 </span>        <span class="prefix">android:</span><span class="attribute">icon</span>=<span class="value">"@android:drawable/star_big_on"</span>
<span class="caretline"><span class="lineno"> 14 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">title</span>=<span class="value">"個人"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 15 </span>
<span class="lineno"> 16 </span>
<span class="lineno"> 17 </span><span class="tag">&lt;/menu></span></pre>

<span class="location"><a href="../../src/main/res/layout/disc2.xml">../../src/main/res/layout/disc2.xml</a>:19</span>: <span class="message">Hardcoded string "TextView", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 16 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"8dp"</span>
<span class="lineno"> 17 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"16dp"</span>
<span class="lineno"> 18 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"16dp"</span>
<span class="caretline"><span class="lineno"> 19 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"TextView"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 20 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintEnd_toEndOf</span>=<span class="value">"parent"</span>
<span class="lineno"> 21 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintStart_toStartOf</span>=<span class="value">"parent"</span>
<span class="lineno"> 22 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintTop_toTopOf</span>=<span class="value">"parent"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/disc2.xml">../../src/main/res/layout/disc2.xml</a>:34</span>: <span class="message">Hardcoded string "&#35531;&#36664;&#20837;&#30041;&#35328;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 31 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"16dp"</span>
<span class="lineno"> 32 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"#ffffff"</span>
<span class="lineno"> 33 </span>        <span class="prefix">android:</span><span class="attribute">ems</span>=<span class="value">"10"</span>
<span class="caretline"><span class="lineno"> 34 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">hint</span>=<span class="value">"請輸入留言"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 35 </span>        <span class="prefix">android:</span><span class="attribute">inputType</span>=<span class="value">"textPersonName"</span>
<span class="lineno"> 36 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintBottom_toBottomOf</span>=<span class="value">"@+id/button3"</span>
<span class="lineno"> 37 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintEnd_toStartOf</span>=<span class="value">"@+id/button3"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/disc2.xml">../../src/main/res/layout/disc2.xml</a>:49</span>: <span class="message">Hardcoded string "&#36865;&#20986;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 46 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"16dp"</span>
<span class="lineno"> 47 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"16dp"</span>
<span class="lineno"> 48 </span>        <span class="prefix">android:</span><span class="attribute">onClick</span>=<span class="value">"newMessage"</span>
<span class="caretline"><span class="lineno"> 49 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"送出"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 50 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintBottom_toBottomOf</span>=<span class="value">"parent"</span>
<span class="lineno"> 51 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintEnd_toEndOf</span>=<span class="value">"parent"</span> />
<span class="lineno"> 52 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/disc2.xml">../../src/main/res/layout/disc2.xml</a>:73</span>: <span class="message">Hardcoded string "TextView", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 70 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 71 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 72 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"2dp"</span>
<span class="caretline"><span class="lineno"> 73 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"TextView"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 74 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintBottom_toTopOf</span>=<span class="value">"@+id/message"</span>
<span class="lineno"> 75 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintStart_toStartOf</span>=<span class="value">"@+id/message"</span> />
<span class="lineno"> 76 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/disc2.xml">../../src/main/res/layout/disc2.xml</a>:83</span>: <span class="message">Hardcoded string "&#35498;:", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 80 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 81 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"8dp"</span>
<span class="lineno"> 82 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 83 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"說:"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 84 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintBottom_toBottomOf</span>=<span class="value">"@+id/nickname"</span>
<span class="lineno"> 85 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintStart_toEndOf</span>=<span class="value">"@+id/nickname"</span>
<span class="lineno"> 86 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintTop_toTopOf</span>=<span class="value">"@+id/nickname"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/disc_content2.xml">../../src/main/res/layout/disc_content2.xml</a>:17</span>: <span class="message">Hardcoded string "TextView", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 14 </span>        <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"left"</span>
<span class="lineno"> 15 </span>        <span class="prefix">android:</span><span class="attribute">lines</span>=<span class="value">"2"</span>
<span class="lineno"> 16 </span>        <span class="prefix">android:</span><span class="attribute">maxLines</span>=<span class="value">"2"</span>
<span class="caretline"><span class="lineno"> 17 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"TextView"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 18 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintEnd_toEndOf</span>=<span class="value">"parent"</span>
<span class="lineno"> 19 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintStart_toStartOf</span>=<span class="value">"parent"</span>
<span class="lineno"> 20 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintTop_toTopOf</span>=<span class="value">"parent"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/disc_content2.xml">../../src/main/res/layout/disc_content2.xml</a>:28</span>: <span class="message">Hardcoded string "TextView", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 25 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 26 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"8dp"</span>
<span class="lineno"> 27 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 28 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"TextView"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 29 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintBottom_toBottomOf</span>=<span class="value">"@+id/textView5"</span>
<span class="lineno"> 30 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintStart_toEndOf</span>=<span class="value">"@+id/textView5"</span>
<span class="lineno"> 31 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintTop_toTopOf</span>=<span class="value">"@+id/textView5"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/disc_content2.xml">../../src/main/res/layout/disc_content2.xml</a>:40</span>: <span class="message">Hardcoded string "&#30041;&#35328;&#32773;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 37 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"8dp"</span>
<span class="lineno"> 38 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="lineno"> 39 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 40 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"留言者"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 41 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintStart_toStartOf</span>=<span class="value">"parent"</span>
<span class="lineno"> 42 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintTop_toBottomOf</span>=<span class="value">"@+id/disccontent"</span> />
<span class="lineno"> 43 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/disc_content2.xml">../../src/main/res/layout/disc_content2.xml</a>:50</span>: <span class="message">Hardcoded string "2018/01/01", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 47 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 48 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"8dp"</span>
<span class="lineno"> 49 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 50 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"2018/01/01"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 51 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintBottom_toBottomOf</span>=<span class="value">"@+id/textView5"</span>
<span class="lineno"> 52 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintStart_toEndOf</span>=<span class="value">"@+id/textView7"</span>
<span class="lineno"> 53 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintTop_toTopOf</span>=<span class="value">"@+id/textView5"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/disc_content2.xml">../../src/main/res/layout/disc_content2.xml</a>:61</span>: <span class="message">Hardcoded string "&#30041;&#35328;&#26178;&#38291;:", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 58 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 59 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"8dp"</span>
<span class="lineno"> 60 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 61 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"留言時間:"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 62 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintBottom_toBottomOf</span>=<span class="value">"@+id/textView5"</span>
<span class="lineno"> 63 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintStart_toEndOf</span>=<span class="value">"@+id/nickname"</span>
<span class="lineno"> 64 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintTop_toTopOf</span>=<span class="value">"@+id/textView5"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_negtive.xml">../../src/main/res/layout/epic_popup_negtive.xml</a>:18</span>: <span class="message">Hardcoded string "TODO", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  15 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span>=<span class="value">"true"</span>
<span class="lineno">  16 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"7dp"</span>
<span class="lineno">  17 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"7dp"</span>
<span class="caretline"><span class="lineno">  18 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">contentDescription</span>=<span class="value">"TODO"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  19 </span>        <span class="prefix">android:</span><span class="attribute">elevation</span>=<span class="value">"5dp"</span>
<span class="lineno">  20 </span>        <span class="prefix">app:</span><span class="attribute">srcCompat</span>=<span class="value">"@drawable/ic_close_black_24dp"</span> />
<span class="lineno">  21 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_negtive.xml">../../src/main/res/layout/epic_popup_negtive.xml</a>:62</span>: <span class="message">Hardcoded string "TODO", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  59 </span>                    <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"20dp"</span>
<span class="lineno">  60 </span>                    <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"10dp"</span>
<span class="lineno">  61 </span>                    <span class="prefix">app:</span><span class="attribute">srcCompat</span>=<span class="value">"@drawable/ic_error_outline_black_24dp"</span>
<span class="caretline"><span class="lineno">  62 </span>                    <span class="warning"><span class="prefix">android:</span><span class="attribute">contentDescription</span>=<span class="value">"TODO"</span></span>><span class="tag">&lt;/ImageView></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  63 </span>
<span class="lineno">  64 </span>                <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno">  65 </span><span class="attribute">                    </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/messageTv"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_negtive.xml">../../src/main/res/layout/epic_popup_negtive.xml</a>:83</span>: <span class="message">Hardcoded string "Try Again", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  80 </span>                    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"200dp"</span>
<span class="lineno">  81 </span>                    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  82 </span>                    <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"20dp"</span>
<span class="caretline"><span class="lineno">  83 </span>                    <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Try Again"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  84 </span>                    <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/colorAccent"</span>
<span class="lineno">  85 </span>                    <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/button_red_round"</span>
<span class="lineno">  86 </span>                    <span class="prefix">android:</span><span class="attribute">layout_gravity</span>=<span class="value">"center_horizontal"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_positive.xml">../../src/main/res/layout/epic_popup_positive.xml</a>:19</span>: <span class="message">Hardcoded string "TODO", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  16 </span>        <span class="prefix">android:</span><span class="attribute">elevation</span>=<span class="value">"5dp"</span>
<span class="lineno">  17 </span>        <span class="prefix">app:</span><span class="attribute">srcCompat</span>=<span class="value">"@drawable/ic_close_black_24dp"</span>
<span class="lineno">  18 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno">  19 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">contentDescription</span>=<span class="value">"TODO"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  20 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"7dp"</span> />
<span class="lineno">  21 </span>
<span class="lineno">  22 </span>    <span class="tag">&lt;androidx.cardview.widget.CardView</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_positive.xml">../../src/main/res/layout/epic_popup_positive.xml</a>:82</span>: <span class="message">Hardcoded string "+57 points", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  79 </span>                    <span class="prefix">android:</span><span class="attribute">textAlignment</span>=<span class="value">"center"</span>
<span class="lineno">  80 </span>                    <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"20dp"</span>
<span class="lineno">  81 </span>                    <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
<span class="caretline"><span class="lineno">  82 </span>                    <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"+57 points"</span></span>><span class="tag">&lt;/TextView></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  83 </span>
<span class="lineno">  84 </span>                <span class="tag">&lt;Button</span><span class="attribute">
</span><span class="lineno">  85 </span><span class="attribute">                    </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btnAccept"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_positive.xml">../../src/main/res/layout/epic_popup_positive.xml</a>:89</span>: <span class="message">Hardcoded string "YESSSSSS", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  86 </span>                    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"200dp"</span>
<span class="lineno">  87 </span>                    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  88 </span>                    <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"20dp"</span>
<span class="caretline"><span class="lineno">  89 </span>                    <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"YESSSSSS"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  90 </span>                    <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/colorAccent"</span>
<span class="lineno">  91 </span>                    <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/button_green_round"</span>
<span class="lineno">  92 </span>                    <span class="prefix">android:</span><span class="attribute">layout_gravity</span>=<span class="value">"center_horizontal"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/fireforulist2.xml">../../src/main/res/layout/fireforulist2.xml</a>:16</span>: <span class="message">Hardcoded string "Refresh", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 13 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 14 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"20dp"</span>
<span class="lineno"> 15 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"#4F5D60AC"</span>
<span class="caretline"><span class="lineno"> 16 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Refresh"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 17 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"#9C27B0"</span>
<span class="lineno"> 18 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintEnd_toEndOf</span>=<span class="value">"parent"</span>
<span class="lineno"> 19 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintHorizontal_bias</span>=<span class="value">"0.473"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fireforulist2.xml">../../src/main/res/layout/fireforulist2.xml</a>:43</span>: <span class="message">Hardcoded string "&#39135;&#35676;&#23560;&#21312;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 40 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 41 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 42 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"20dp"</span>
<span class="caretline"><span class="lineno"> 43 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"食譜專區"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 44 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"30sp"</span>
<span class="lineno"> 45 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintEnd_toEndOf</span>=<span class="value">"parent"</span>
<span class="lineno"> 46 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintStart_toStartOf</span>=<span class="value">"parent"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/forum_listitem2.xml">../../src/main/res/layout/forum_listitem2.xml</a>:32</span>: <span class="message">Hardcoded string "2018/01/01", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 29 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 30 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="lineno"> 31 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 32 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"2018/01/01"</span></span>><span class="tag">&lt;/TextView></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 33 </span>
<span class="lineno"> 34 </span>            <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 35 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/lastUpdateUserNickname"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/forum_listitem2.xml">../../src/main/res/layout/forum_listitem2.xml</a>:40</span>: <span class="message">Hardcoded string "TextView", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 37 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 38 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="lineno"> 39 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 40 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"TextView"</span></span>><span class="tag">&lt;/TextView></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 41 </span>
<span class="lineno"> 42 </span>            <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 43 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/textView4"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/forum_listitem2.xml">../../src/main/res/layout/forum_listitem2.xml</a>:47</span>: <span class="message">Hardcoded string "&#26368;&#24460;&#26356;&#26032;:", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 44 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 45 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 46 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 47 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"最後更新:"</span></span>><span class="tag">&lt;/TextView></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 48 </span>
<span class="lineno"> 49 </span>            <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 50 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/textView6"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/forum_listitem2.xml">../../src/main/res/layout/forum_listitem2.xml</a>:55</span>: <span class="message">Hardcoded string "&#25512;&#34214;&#32102;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 52 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 53 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="lineno"> 54 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 55 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"推薦給"</span></span>><span class="tag">&lt;/TextView></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 56 </span>        <span class="tag">&lt;/LinearLayout></span>
<span class="lineno"> 57 </span>
<span class="lineno"> 58 </span>        <span class="tag">&lt;TextView</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/forum_listitem2.xml">../../src/main/res/layout/forum_listitem2.xml</a>:62</span>: <span class="message">Hardcoded string "Subject", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 59 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/subject"</span>
<span class="lineno"> 60 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 61 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno"> 62 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Subject"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 63 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"22dp"</span>><span class="tag">&lt;/TextView></span>
<span class="lineno"> 64 </span>
<span class="lineno"> 65 </span>    <span class="tag">&lt;/LinearLayout></span>
</pre>

<br/><b>NOTE: 34 results omitted.</b><br/><br/></div>
</div>
<div class="metadata"><div class="explanation" id="explanationHardcodedText" style="display: none;">
Hardcoding text attributes directly in layout files is bad for several reasons:<br/>
<br/>
* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)<br/>
<br/>
* The application cannot be translated to other languages by just adding new translations for existing string resources.<br/>
<br/>
There are quickfixes to automatically extract this hardcoded string into a resource lookup.<br/>To suppress this error, use the issue id "HardcodedText" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">HardcodedText</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationHardcodedTextLink" onclick="reveal('explanationHardcodedText');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="HardcodedTextCardLink" onclick="hideid('HardcodedTextCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Internationalization:Bidirectional Text"></a>
<a name="RtlHardcoded"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RtlHardcodedCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using left/right instead of start/end attributes</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:83</span>: <span class="message">Redundant attribute <code>layout_marginLeft</code>; already defining <code>layout_marginStart</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno">  80 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"80dp"</span>
<span class="lineno">  81 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"59dp"</span>
<span class="lineno">  82 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"16dp"</span>
<span class="caretline"><span class="lineno">  83 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"16dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  84 </span>                <span class="prefix">android:</span><span class="attribute">onClick</span>=<span class="value">"buyphoto"</span>
<span class="lineno">  85 </span>                <span class="prefix">app:</span><span class="attribute">layout_constraintStart_toStartOf</span>=<span class="value">"parent"</span>
<span class="lineno">  86 </span>                <span class="prefix">app:</span><span class="attribute">srcCompat</span>=<span class="value">"@drawable/takepic"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:119</span>: <span class="message">Consider replacing <code>android:layout_marginLeft</code> with <code>android:layout_marginStart="25dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 116 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/imv2"</span>
<span class="lineno"> 117 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"60dp"</span>
<span class="lineno"> 118 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno"> 119 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"25dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 120 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"5dp"</span>
<span class="lineno"> 121 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"10dp"</span>
<span class="lineno"> 122 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"5dp"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:121</span>: <span class="message">Consider replacing <code>android:layout_marginRight</code> with <code>android:layout_marginEnd="10dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 118 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 119 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"25dp"</span>
<span class="lineno"> 120 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"5dp"</span>
<span class="caretline"><span class="lineno"> 121 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginRight</span></span>=<span class="value">"10dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 122 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"5dp"</span> />
<span class="lineno"> 123 </span>
<span class="lineno"> 124 </span>            <span class="tag">&lt;TextView</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_buy.xml">../../src/main/res/layout/activity_buy.xml</a>:317</span>: <span class="message">Consider replacing <code>android:layout_marginRight</code> with <code>android:layout_marginEnd="10dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 314 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/etRemarks"</span>
<span class="lineno"> 315 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 316 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno"> 317 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginRight</span></span>=<span class="value">"10dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 318 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 319 </span>                <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/outside"</span>
<span class="lineno"> 320 </span>                <span class="prefix">android:</span><span class="attribute">ems</span>=<span class="value">"10"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/disc2.xml">../../src/main/res/layout/disc2.xml</a>:15</span>: <span class="message">Redundant attribute <code>layout_marginLeft</code>; already defining <code>layout_marginStart</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno"> 12 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno"> 13 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 14 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 15 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"16dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 16 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"8dp"</span>
<span class="lineno"> 17 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"16dp"</span>
<span class="lineno"> 18 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"16dp"</span>
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="RtlHardcodedDivLink" onclick="reveal('RtlHardcodedDiv');" />+ 31 More Occurrences...</button>
<div id="RtlHardcodedDiv" style="display: none">
<span class="location"><a href="../../src/main/res/layout/disc2.xml">../../src/main/res/layout/disc2.xml</a>:16</span>: <span class="message">Redundant attribute <code>layout_marginRight</code>; already defining <code>layout_marginEnd</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno"> 13 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 14 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"8dp"</span>
<span class="lineno"> 15 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"16dp"</span>
<span class="caretline"><span class="lineno"> 16 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginRight</span></span>=<span class="value">"8dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 17 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"16dp"</span>
<span class="lineno"> 18 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"16dp"</span>
<span class="lineno"> 19 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"TextView"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/disc2.xml">../../src/main/res/layout/disc2.xml</a>:29</span>: <span class="message">Redundant attribute <code>layout_marginLeft</code>; already defining <code>layout_marginStart</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno"> 26 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno"> 27 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 28 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 29 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"16dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 30 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"8dp"</span>
<span class="lineno"> 31 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"16dp"</span>
<span class="lineno"> 32 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"#ffffff"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/disc2.xml">../../src/main/res/layout/disc2.xml</a>:30</span>: <span class="message">Redundant attribute <code>layout_marginRight</code>; already defining <code>layout_marginEnd</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno"> 27 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 28 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"8dp"</span>
<span class="lineno"> 29 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"16dp"</span>
<span class="caretline"><span class="lineno"> 30 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginRight</span></span>=<span class="value">"8dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 31 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"16dp"</span>
<span class="lineno"> 32 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"#ffffff"</span>
<span class="lineno"> 33 </span>        <span class="prefix">android:</span><span class="attribute">ems</span>=<span class="value">"10"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/disc2.xml">../../src/main/res/layout/disc2.xml</a>:47</span>: <span class="message">Redundant attribute <code>layout_marginRight</code>; already defining <code>layout_marginEnd</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno"> 44 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 45 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"16dp"</span>
<span class="lineno"> 46 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"16dp"</span>
<span class="caretline"><span class="lineno"> 47 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginRight</span></span>=<span class="value">"16dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 48 </span>        <span class="prefix">android:</span><span class="attribute">onClick</span>=<span class="value">"newMessage"</span>
<span class="lineno"> 49 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"送出"</span>
<span class="lineno"> 50 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintBottom_toBottomOf</span>=<span class="value">"parent"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/disc2.xml">../../src/main/res/layout/disc2.xml</a>:81</span>: <span class="message">Redundant attribute <code>layout_marginLeft</code>; already defining <code>layout_marginStart</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno"> 78 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/textView3"</span>
<span class="lineno"> 79 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 80 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 81 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"8dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 82 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="lineno"> 83 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"說:"</span>
<span class="lineno"> 84 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintBottom_toBottomOf</span>=<span class="value">"@+id/nickname"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/disc_content2.xml">../../src/main/res/layout/disc_content2.xml</a>:14</span>: <span class="message">Use "<code>start</code>" instead of "<code>left</code>" to ensure correct behavior in right-to-left locales</span><br /><pre class="errorlines">
<span class="lineno"> 11 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/disccontent"</span>
<span class="lineno"> 12 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno"> 13 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 14 </span>        <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"</span><span class="warning"><span class="value">left</span></span><span class="value">"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 15 </span>        <span class="prefix">android:</span><span class="attribute">lines</span>=<span class="value">"2"</span>
<span class="lineno"> 16 </span>        <span class="prefix">android:</span><span class="attribute">maxLines</span>=<span class="value">"2"</span>
<span class="lineno"> 17 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"TextView"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/disc_content2.xml">../../src/main/res/layout/disc_content2.xml</a>:26</span>: <span class="message">Redundant attribute <code>layout_marginLeft</code>; already defining <code>layout_marginStart</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno"> 23 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/nickname"</span>
<span class="lineno"> 24 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 25 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 26 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"8dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 27 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="lineno"> 28 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"TextView"</span>
<span class="lineno"> 29 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintBottom_toBottomOf</span>=<span class="value">"@+id/textView5"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/disc_content2.xml">../../src/main/res/layout/disc_content2.xml</a>:37</span>: <span class="message">Redundant attribute <code>layout_marginLeft</code>; already defining <code>layout_marginStart</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno"> 34 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/textView5"</span>
<span class="lineno"> 35 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 36 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 37 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"8dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 38 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="lineno"> 39 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"8dp"</span>
<span class="lineno"> 40 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"留言者"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/disc_content2.xml">../../src/main/res/layout/disc_content2.xml</a>:48</span>: <span class="message">Redundant attribute <code>layout_marginLeft</code>; already defining <code>layout_marginStart</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno"> 45 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/disctime"</span>
<span class="lineno"> 46 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 47 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 48 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"8dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 49 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="lineno"> 50 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"2018/01/01"</span>
<span class="lineno"> 51 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintBottom_toBottomOf</span>=<span class="value">"@+id/textView5"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/disc_content2.xml">../../src/main/res/layout/disc_content2.xml</a>:59</span>: <span class="message">Redundant attribute <code>layout_marginLeft</code>; already defining <code>layout_marginStart</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno"> 56 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/textView7"</span>
<span class="lineno"> 57 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 58 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 59 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"8dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 60 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="lineno"> 61 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"留言時間:"</span>
<span class="lineno"> 62 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintBottom_toBottomOf</span>=<span class="value">"@+id/textView5"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_negtive.xml">../../src/main/res/layout/epic_popup_negtive.xml</a>:15</span>: <span class="message">Consider replacing <code>android:layout_alignParentRight</code> with <code>android:layout_alignParentEnd="true"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno">  12 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/closePopupNegativeImg"</span>
<span class="lineno">  13 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  14 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  15 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span></span>=<span class="value">"true"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  16 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"7dp"</span>
<span class="lineno">  17 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"7dp"</span>
<span class="lineno">  18 </span>        <span class="prefix">android:</span><span class="attribute">contentDescription</span>=<span class="value">"TODO"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_negtive.xml">../../src/main/res/layout/epic_popup_negtive.xml</a>:17</span>: <span class="message">Consider replacing <code>android:layout_marginRight</code> with <code>android:layout_marginEnd="7dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno">  14 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  15 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span>=<span class="value">"true"</span>
<span class="lineno">  16 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"7dp"</span>
<span class="caretline"><span class="lineno">  17 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginRight</span></span>=<span class="value">"7dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  18 </span>        <span class="prefix">android:</span><span class="attribute">contentDescription</span>=<span class="value">"TODO"</span>
<span class="lineno">  19 </span>        <span class="prefix">android:</span><span class="attribute">elevation</span>=<span class="value">"5dp"</span>
<span class="lineno">  20 </span>        <span class="prefix">app:</span><span class="attribute">srcCompat</span>=<span class="value">"@drawable/ic_close_black_24dp"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_negtive.xml">../../src/main/res/layout/epic_popup_negtive.xml</a>:28</span>: <span class="message">Redundant attribute <code>layout_alignParentLeft</code>; already defining <code>layout_alignParentStart</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno">  25 </span>        <span class="prefix">app:</span><span class="attribute">cardCornerRadius</span>=<span class="value">"15dp"</span>
<span class="lineno">  26 </span>        <span class="prefix">app:</span><span class="attribute">cardBackgroundColor</span>=<span class="value">"@color/colorRed"</span>
<span class="lineno">  27 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentTop</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno">  28 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_alignParentLeft</span></span>=<span class="value">"true"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  29 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentStart</span>=<span class="value">"true"</span>>
<span class="lineno">  30 </span>
<span class="lineno">  31 </span>        <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_positive.xml">../../src/main/res/layout/epic_popup_positive.xml</a>:15</span>: <span class="message">Redundant attribute <code>layout_marginRight</code>; already defining <code>layout_marginEnd</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno">  12 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  13 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  14 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"7dp"</span>
<span class="caretline"><span class="lineno">  15 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginRight</span></span>=<span class="value">"7dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  16 </span>        <span class="prefix">android:</span><span class="attribute">elevation</span>=<span class="value">"5dp"</span>
<span class="lineno">  17 </span>        <span class="prefix">app:</span><span class="attribute">srcCompat</span>=<span class="value">"@drawable/ic_close_black_24dp"</span>
<span class="lineno">  18 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span>=<span class="value">"true"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_positive.xml">../../src/main/res/layout/epic_popup_positive.xml</a>:18</span>: <span class="message">Consider replacing <code>android:layout_alignParentRight</code> with <code>android:layout_alignParentEnd="true"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno">  15 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"7dp"</span>
<span class="lineno">  16 </span>        <span class="prefix">android:</span><span class="attribute">elevation</span>=<span class="value">"5dp"</span>
<span class="lineno">  17 </span>        <span class="prefix">app:</span><span class="attribute">srcCompat</span>=<span class="value">"@drawable/ic_close_black_24dp"</span>
<span class="caretline"><span class="lineno">  18 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span></span>=<span class="value">"true"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  19 </span>        <span class="prefix">android:</span><span class="attribute">contentDescription</span>=<span class="value">"TODO"</span>
<span class="lineno">  20 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"7dp"</span> />
<span class="lineno">  21 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/epic_popup_positive.xml">../../src/main/res/layout/epic_popup_positive.xml</a>:28</span>: <span class="message">Redundant attribute <code>layout_alignParentLeft</code>; already defining <code>layout_alignParentStart</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno">  25 </span>        <span class="prefix">app:</span><span class="attribute">cardCornerRadius</span>=<span class="value">"15dp"</span>
<span class="lineno">  26 </span>        <span class="prefix">app:</span><span class="attribute">cardBackgroundColor</span>=<span class="value">"@color/colorGreenLight"</span>
<span class="lineno">  27 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentTop</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno">  28 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_alignParentLeft</span></span>=<span class="value">"true"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  29 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentStart</span>=<span class="value">"true"</span>>
<span class="lineno">  30 </span>
<span class="lineno">  31 </span>        <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/forum_listitem2.xml">../../src/main/res/layout/forum_listitem2.xml</a>:31</span>: <span class="message">Redundant attribute <code>layout_marginLeft</code>; already defining <code>layout_marginStart</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno"> 28 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 29 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 30 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 31 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"8dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 32 </span>                <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"2018/01/01"</span>><span class="tag">&lt;/TextView></span>
<span class="lineno"> 33 </span>
<span class="lineno"> 34 </span>            <span class="tag">&lt;TextView</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/forum_listitem2.xml">../../src/main/res/layout/forum_listitem2.xml</a>:39</span>: <span class="message">Redundant attribute <code>layout_marginLeft</code>; already defining <code>layout_marginStart</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno"> 36 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 37 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 38 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 39 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"8dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 40 </span>                <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"TextView"</span>><span class="tag">&lt;/TextView></span>
<span class="lineno"> 41 </span>
<span class="lineno"> 42 </span>            <span class="tag">&lt;TextView</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/forum_listitem2.xml">../../src/main/res/layout/forum_listitem2.xml</a>:54</span>: <span class="message">Redundant attribute <code>layout_marginLeft</code>; already defining <code>layout_marginStart</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno"> 51 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 52 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 53 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 54 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"8dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 55 </span>                <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"推薦給"</span>><span class="tag">&lt;/TextView></span>
<span class="lineno"> 56 </span>        <span class="tag">&lt;/LinearLayout></span>
<span class="lineno"> 57 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_favorites.xml">../../src/main/res/layout/fragment_favorites.xml</a>:15</span>: <span class="message">Redundant attribute <code>layout_marginRight</code>; already defining <code>layout_marginEnd</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno"> 12 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 13 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 14 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 15 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginRight</span></span>=<span class="value">"8dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 16 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"84dp"</span>
<span class="lineno"> 17 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"請輸入暱稱"</span>
<span class="lineno"> 18 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintEnd_toEndOf</span>=<span class="value">"parent"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_favorites.xml">../../src/main/res/layout/fragment_favorites.xml</a>:71</span>: <span class="message">Redundant attribute <code>layout_marginRight</code>; already defining <code>layout_marginEnd</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno"> 68 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 69 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"8dp"</span>
<span class="lineno"> 70 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 71 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginRight</span></span>=<span class="value">"8dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 72 </span>        <span class="prefix">android:</span><span class="attribute">onClick</span>=<span class="value">"enterForum"</span>
<span class="lineno"> 73 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"食譜推薦區"</span>
<span class="lineno"> 74 </span>        <span class="prefix">app:</span><span class="attribute">layout_constraintEnd_toEndOf</span>=<span class="value">"@+id/nickname"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_home.xml">../../src/main/res/layout/fragment_home.xml</a>:14</span>: <span class="message">Redundant attribute <code>layout_alignParentLeft</code>; already defining <code>layout_alignParentStart</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno">  11 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"150dp"</span>
<span class="lineno">  12 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"150dp"</span>
<span class="lineno">  13 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentStart</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno">  14 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_alignParentLeft</span></span>=<span class="value">"true"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  15 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentTop</span>=<span class="value">"true"</span>
<span class="lineno">  16 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentEnd</span>=<span class="value">"true"</span>
<span class="lineno">  17 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span>=<span class="value">"true"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_home.xml">../../src/main/res/layout/fragment_home.xml</a>:17</span>: <span class="message">Redundant attribute <code>layout_alignParentRight</code>; already defining <code>layout_alignParentEnd</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno">  14 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentLeft</span>=<span class="value">"true"</span>
<span class="lineno">  15 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentTop</span>=<span class="value">"true"</span>
<span class="lineno">  16 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentEnd</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno">  17 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span></span>=<span class="value">"true"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  18 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="lineno">  19 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"8dp"</span>
<span class="lineno">  20 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"8dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_home.xml">../../src/main/res/layout/fragment_home.xml</a>:39</span>: <span class="message">Redundant attribute <code>layout_alignParentLeft</code>; already defining <code>layout_alignParentStart</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno">  36 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"150dp"</span>
<span class="lineno">  37 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"150dp"</span>
<span class="lineno">  38 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentStart</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno">  39 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_alignParentLeft</span></span>=<span class="value">"true"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  40 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentTop</span>=<span class="value">"true"</span>
<span class="lineno">  41 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentEnd</span>=<span class="value">"true"</span>
<span class="lineno">  42 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span>=<span class="value">"true"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_home.xml">../../src/main/res/layout/fragment_home.xml</a>:42</span>: <span class="message">Redundant attribute <code>layout_alignParentRight</code>; already defining <code>layout_alignParentEnd</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno">  39 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentLeft</span>=<span class="value">"true"</span>
<span class="lineno">  40 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentTop</span>=<span class="value">"true"</span>
<span class="lineno">  41 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentEnd</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno">  42 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span></span>=<span class="value">"true"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  43 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="lineno">  44 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"8dp"</span>
<span class="lineno">  45 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"8dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_home.xml">../../src/main/res/layout/fragment_home.xml</a>:63</span>: <span class="message">Redundant attribute <code>layout_alignParentLeft</code>; already defining <code>layout_alignParentStart</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno">  60 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"150dp"</span>
<span class="lineno">  61 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"150dp"</span>
<span class="lineno">  62 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentStart</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno">  63 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_alignParentLeft</span></span>=<span class="value">"true"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  64 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentTop</span>=<span class="value">"true"</span>
<span class="lineno">  65 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentEnd</span>=<span class="value">"true"</span>
<span class="lineno">  66 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span>=<span class="value">"true"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_home.xml">../../src/main/res/layout/fragment_home.xml</a>:66</span>: <span class="message">Redundant attribute <code>layout_alignParentRight</code>; already defining <code>layout_alignParentEnd</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno">  63 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentLeft</span>=<span class="value">"true"</span>
<span class="lineno">  64 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentTop</span>=<span class="value">"true"</span>
<span class="lineno">  65 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentEnd</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno">  66 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span></span>=<span class="value">"true"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  67 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="lineno">  68 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"8dp"</span>
<span class="lineno">  69 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"8dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_home.xml">../../src/main/res/layout/fragment_home.xml</a>:87</span>: <span class="message">Redundant attribute <code>layout_alignParentLeft</code>; already defining <code>layout_alignParentStart</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno">  84 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"150dp"</span>
<span class="lineno">  85 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"150dp"</span>
<span class="lineno">  86 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentStart</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno">  87 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_alignParentLeft</span></span>=<span class="value">"true"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  88 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentTop</span>=<span class="value">"true"</span>
<span class="lineno">  89 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentEnd</span>=<span class="value">"true"</span>
<span class="lineno">  90 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span>=<span class="value">"true"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_home.xml">../../src/main/res/layout/fragment_home.xml</a>:90</span>: <span class="message">Redundant attribute <code>layout_alignParentRight</code>; already defining <code>layout_alignParentEnd</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno">  87 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentLeft</span>=<span class="value">"true"</span>
<span class="lineno">  88 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentTop</span>=<span class="value">"true"</span>
<span class="lineno">  89 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentEnd</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno">  90 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span></span>=<span class="value">"true"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  91 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="lineno">  92 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"8dp"</span>
<span class="lineno">  93 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"8dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/item.xml">../../src/main/res/layout/item.xml</a>:23</span>: <span class="message">Consider replacing <code>android:layout_alignParentRight</code> with <code>android:layout_alignParentEnd="true"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 20 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/name1"</span>
<span class="lineno"> 21 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 22 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 23 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span></span>=<span class="value">"true"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 24 </span>            <span class="prefix">android:</span><span class="attribute">layout_alignParentBottom</span>=<span class="value">"true"</span>
<span class="lineno"> 25 </span>            <span class="prefix">android:</span><span class="attribute">layout_toRightOf</span>=<span class="value">"@+id/photo"</span>
<span class="lineno"> 26 </span>            <span class="prefix">android:</span><span class="attribute">ellipsize</span>=<span class="value">"marquee"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/item.xml">../../src/main/res/layout/item.xml</a>:25</span>: <span class="message">Consider replacing <code>android:layout_toRightOf</code> with <code>android:layout_toEndOf="@+id/photo"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 22 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 23 </span>            <span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span>=<span class="value">"true"</span>
<span class="lineno"> 24 </span>            <span class="prefix">android:</span><span class="attribute">layout_alignParentBottom</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno"> 25 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_toRightOf</span></span>=<span class="value">"@+id/photo"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 26 </span>            <span class="prefix">android:</span><span class="attribute">ellipsize</span>=<span class="value">"marquee"</span>
<span class="lineno"> 27 </span>            <span class="prefix">android:</span><span class="attribute">singleLine</span>=<span class="value">"true"</span>
<span class="lineno"> 28 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"20dip"</span> />
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationRtlHardcoded" style="display: none;">
Using <code>Gravity#LEFT</code> and <code>Gravity#RIGHT</code> can lead to problems when a layout is rendered in locales where text flows from right to left. Use <code>Gravity#START</code> and <code>Gravity#END</code> instead. Similarly, in XML <code>gravity</code> and <code>layout_gravity</code> attributes, use <code>start</code> rather than <code>left</code>.<br/>
<br/>
For XML attributes such as paddingLeft and <code>layout_marginLeft</code>, use <code>paddingStart</code> and <code>layout_marginStart</code>. <b>NOTE</b>: If your <code>minSdkVersion</code> is less than 17, you should add <b>both</b> the older left/right attributes <b>as well as</b> the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.<br/>
<br/>
(Note: For <code>Gravity#LEFT</code> and <code>Gravity#START</code>, you can use these constants even when targeting older platforms, because the <code>start</code> bitmask is a superset of the <code>left</code> bitmask. Therefore, you can use <code>gravity="start"</code> rather than <code>gravity="left|start"</code>.)<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "RtlHardcoded" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">RtlHardcoded</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Bidirectional Text</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRtlHardcodedLink" onclick="reveal('explanationRtlHardcoded');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RtlHardcodedCardLink" onclick="hideid('RtlHardcodedCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="MissingIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Disabled Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
One or more issues were not run by lint, either
because the check is not enabled by default, or because
it was disabled with a command line flag or via one or
more <code>lint.xml</code> configuration files in the project directories.
<div id="SuppressedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">AppCompatMethod<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
When using the appcompat library, there are some methods you should be calling instead of the normal ones; for example, <code>getSupportActionBar()</code> instead of <code>getActionBar()</code>. This lint check looks for calls to the wrong method.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/libraries/support-library/">https://developer.android.com/topic/libraries/support-library/</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AppLinksAutoVerify<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that app links are correctly set and associated with website.<br/><div class="moreinfo">More info: <a href="https://g.co/appindexing/applinks">https://g.co/appindexing/applinks</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BackButton<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
According to the Android Design Guide,<br/>
<br/>
"Other platforms use an explicit back button with label to allow the user to navigate up the application's hierarchy. Instead, Android uses the main action bar's app icon for hierarchical navigation and the navigation bar's back button for temporal navigation."<br/>
<br/>
This check is not very sophisticated (it just looks for buttons with the label "Back"), so it is disabled by default to not trigger on common scenarios like pairs of Back/Next buttons to paginate through screens.<br/><div class="moreinfo">More info: <a href="https://material.io/design/">https://material.io/design/</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ConvertToWebp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The WebP format is typically more compact than PNG and JPEG. As of Android 4.2.1 it supports transparency and lossless conversion as well. Note that there is a quickfix in the IDE which lets you perform conversion.<br/>
<br/>
Previously, launcher icons were required to be in the PNG format but that restriction is no longer there, so lint now flags these.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DalvikOverride<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The Dalvik virtual machine will treat a package private method in one class as overriding a package private method in its super class, even if they are in separate packages.<br/>
<br/>
If you really did intend for this method to override the other, make the method <code>protected</code> instead.<br/>
<br/>
If you did <b>not</b> intend the override, consider making the method private, or changing its name or signature.<br/>
<br/>
Note that this check is disabled be default, because ART (the successor to Dalvik) no longer has this behavior.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DefaultEncoding<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Some APIs will implicitly use the default system character encoding instead of UTF-8 when converting to or from bytes, such as when creating a default <code>FileReader</code>.<br/>
<br/>
This is <i>usually</i> not correct; you only want to do this if you need to read files created by other programs where they have deliberately written in the same encoding. The default encoding varies from platform to platform and can vary from locale to locale, so this makes it difficult to interpret files containing non-ASCII characters.<br/>
<br/>
We recommend using UTF-8 everywhere.<br/>
<br/>
Note that on Android, the default file encoding is always UTF-8 (see <a href="https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(">https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(</a>) for more), so this lint check deliberately does not flag any problems in Android code, since it is always safe to rely on the default character encoding there.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DuplicateStrings<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Duplicate strings can make applications larger unnecessarily.<br/>
<br/>
This lint check looks for duplicate strings, including differences for strings where the only difference is in capitalization. Title casing and all uppercase can all be adjusted in the layout or in code.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType">https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EasterEgg<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
An "easter egg" is code deliberately hidden in the code, both from potential users and even from other developers. This lint check looks for code which looks like it may be hidden from sight.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExpensiveAssertion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
In Kotlin, assertions are not handled the same way as from the Java programming language. In particular, they're just implemented as a library call, and inside the library call the error is only thrown if assertions are enabled.<br/>
<br/>
This means that the arguments to the <code>assert</code> call will <b>always</b> be evaluated. If you're doing any computation in the expression being asserted, that computation will unconditionally be performed whether or not assertions are turned on. This typically turns into wasted work in release builds.<br/>
<br/>
This check looks for cases where the assertion condition is nontrivial, e.g. it is performing method calls or doing more work than simple comparisons on local variables or fields.<br/>
<br/>
You can work around this by writing your own inline assert method instead:<br/>

<pre>
@Suppress("INVISIBLE_REFERENCE", "INVISIBLE_MEMBER")
inline fun assert(condition: () -> Boolean) {
    if (_Assertions.ENABLED &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>
In Android, because assertions are not enforced at runtime, instead use this:<br/>

<pre>
inline fun assert(condition: () -> Boolean) {
    if (BuildConfig.DEBUG &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconExpectedSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
There are predefined sizes (for each density) for launcher icons. You should follow these conventions to make sure your icons fit in with the overall look of the platform.<br/><div class="moreinfo">More info: <a href="https://material.io/design/iconography/">https://material.io/design/iconography/</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ImplicitSamInstance<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Kotlin's support for SAM (single accessor method) interfaces lets you pass a lambda to the interface. This will create a new instance on the fly even though there is no explicit constructor call. If you pass one of these lambdas or method references into a method which (for example) stores or compares the object identity, unexpected results may happen.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidPackage<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check scans through libraries looking for calls to APIs that are not included in Android.<br/>
<br/>
When you create Android projects, the classpath is set up such that you can only access classes in the API packages that are included in Android. However, if you add other projects to your libs/ folder, there is no guarantee that those .jar files were built with an Android specific classpath, and in particular, they could be accessing unsupported APIs such as java.applet.<br/>
<br/>
This check scans through library jars and looks for references to API packages that are not included in Android and flags these. This is only an error if your code calls one of the library classes which wind up referencing the unsupported package.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlinPropertyAccess<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
For a method to be represented as a property in Kotlin, strict &#8220;bean&#8221;-style prefixing must be used.<br/>
<br/>
Accessor methods require a <code>get</code> prefix or for boolean-returning methods an <code>is</code> prefix can be used.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#property-prefixes">https://android.github.io/kotlin-guides/interop.html#property-prefixes</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlincFE10<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
K2, the new version of Kotlin compiler, which encompasses the new frontend, is coming. Try to avoid using internal APIs from the old frontend if possible.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LambdaLast<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve calling this code from Kotlin, parameter types eligible for SAM conversion should be last.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last">https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintDocExample<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Lint's tool for generating documentation for each issue has special support for including a code example which shows how to trigger the report. It will pick the first unit test it can find and pick out the source file referenced from the error message, but you can instead designate a unit test to be the documentation example, and in that case, all the files are included.<br/>
<br/>
To designate a unit test as the documentation example for an issue, name the test <code>testDocumentationExample</code>, or if your detector reports multiple issues, <code>testDocumentationExample</code>&lt;Id>, such as <code>testDocumentationExampleMyId</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplPsiEquals<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
You should never compare two PSI elements for equality with <code>equals</code>; use <code>isEquivalentTo(PsiElement)</code> instead.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplUnexpectedDomain<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This checks flags URLs to domains that have not been explicitly allowed for use as a documentation source.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LogConditional<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>BuildConfig</code> class provides a constant, <code>DEBUG</code>, which indicates whether the code is being built in release mode or in debug mode. In release mode, you typically want to strip out all the logging calls. Since the compiler will automatically remove all code which is inside a <code>if (false)</code> check, surrounding your logging calls with a check for <code>BuildConfig.DEBUG</code> is a good idea.<br/>
<br/>
If you <b>really</b> intend for the logging to be present in release mode, you can suppress this warning with a <code>@SuppressLint</code> annotation for the intentional logging calls.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MangledCRLF<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
On Windows, line endings are typically recorded as carriage return plus newline: \r\n.<br/>
<br/>
This detector looks for invalid line endings with repeated carriage return characters (without newlines). Previous versions of the ADT plugin could accidentally introduce these into the file, and when editing the file, the editor could produce confusing visual artifacts.<br/><div class="moreinfo">More info: <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421">https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MinSdkTooLow<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The value of the <code>minSdkVersion</code> property is too low and can be incremented without noticeably reducing the number of supported devices.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NegativeMargin<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Margin values should be positive. Negative values are generally a sign that you are making assumptions about views surrounding the current one, or may be tempted to turn off child clipping to allow a view to escape its parent. Turning off child clipping to do this not only leads to poor graphical performance, it also results in wrong touch event handling since touch events are based strictly on a chain of parent-rect hit tests. Finally, making assumptions about the size of strings can lead to localization problems.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NewerVersionAvailable<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the <code>GradleDependency</code> check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also <b>much</b> slower.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoHardKeywords<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Do not use Kotlin&#8217;s hard keywords as the name of methods or fields. These require the use of backticks to escape when calling from Kotlin. Soft keywords, modifier keywords, and special identifiers are allowed.<br/>
<br/>
For example, ActionEvent's <code>getWhen()</code> method requires backticks when used from Kotlin:
<pre>
val timestamp = event.`when`
</pre>
<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#no-hard-keywords">https://android.github.io/kotlin-guides/interop.html#no-hard-keywords</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoOp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check looks for code which looks like it's a no-op -- usually leftover expressions from interactive debugging, but in some cases bugs where you had intended to do something with the expression such as assign it to a field.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>pure-getters</b> (default is false):<br/>
Whether to assume methods with getter-names have no side effects.<br/>
<br/>
Getter methods (where names start with <code>get</code> or <code>is</code>, and have non-void return types, and no arguments) should not have side effects. With this option turned on, lint will assume that is the case and will list any getter calls whose results are ignored as suspicious code.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"NoOp"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"pure-getters"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PermissionNamingConvention<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Permissions should be prefixed with an app's package name, using reverse-domain-style naming. This prefix should be followed by <code>.permission.</code>, and then a description of the capability that the permission represents, in upper SNAKE_CASE. For example, <code>com.example.myapp.permission.ENGAGE_HYPERSPACE</code>.<br/>
<br/>
Following this recommendation avoids naming collisions, and helps clearly identify the owner and intention of a custom permission.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Registered<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Activities, services and content providers should be registered in the <code>AndroidManifest.xml</code> file using <code>&lt;activity></code>, <code>&lt;service></code> and <code>&lt;provider></code> tags.<br/>
<br/>
If your activity is simply a parent class intended to be subclassed by other "real" activities, make it an abstract class.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/manifest-intro.html">https://developer.android.com/guide/topics/manifest/manifest-intro.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RequiredSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
All views must specify an explicit <code>layout_width</code> and <code>layout_height</code> attribute. There is a runtime check for this, so if you fail to specify a size, an exception is thrown at runtime.<br/>
<br/>
It's possible to specify these widths via styles as well. GridLayout, as a special case, does not require you to specify a size.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SelectableText<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
If a <code>&lt;TextView></code> is used to display data, the user might want to copy that data and paste it elsewhere. To allow this, the <code>&lt;TextView></code> should specify <code>android:textIsSelectable="true"</code>.<br/>
<br/>
This lint check looks for TextViews which are likely to be displaying data: views whose text is set dynamically.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StopShip<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Using the comment <code>// STOPSHIP</code> can be used to flag code that is incomplete but checked in. This comment marker can be used to indicate that the code should not be shipped until the issue is addressed, and lint will look for these. In Gradle projects, this is only checked for non-debug (release) builds.<br/>
<br/>
In Kotlin, the <code>TODO()</code> method is also treated as a stop ship marker; you can use it to make incomplete code compile, but it will throw an exception at runtime and therefore should be fixed before shipping releases.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StringFormatTrivial<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Every call to <code>String.format</code> creates a new <code>Formatter</code> instance, which will decrease the performance of your app. <code>String.format</code> should only be used when necessary--if the formatted string contains only trivial conversions (e.g. <code>b</code>, <code>s</code>, <code>c</code>) and there are no translation concerns, it will be more efficient to replace them and concatenate with <code>+</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SyntheticAccessor<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
A private inner class which is accessed from the outer class will force the compiler to insert a synthetic accessor; this means that you are causing extra overhead. This is not important in small projects, but is important for large apps running up against the 64K method handle limit, and especially for <b>libraries</b> where you want to make sure your library is as small as possible for the cases where your library is used in an app running up against the 64K limit.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TypographyQuotes<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Straight single quotes and double quotes, when used as a pair, can be replaced by "curvy quotes" (or directional quotes). This can make the text more readable. Note that you should never use grave accents and apostrophes to quote, `like this'. (Also note that you should not use curvy quotes for code fragments.)<br/><div class="moreinfo">More info: <a href="https://en.wikipedia.org/wiki/Quotation_mark">https://en.wikipedia.org/wiki/Quotation_mark</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnknownNullness<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve referencing this code from Kotlin, consider adding explicit nullness information here with either <code>@NonNull</code> or <code>@Nullable</code>.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>ignore-deprecated</b> (default is false):<br/>
Whether to ignore classes and members that have been annotated with <code>@Deprecated</code>.<br/>
<br/>
Normally this lint check will flag all unannotated elements, but by setting this option to <code>true</code> it will skip any deprecated elements.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UnknownNullness"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"ignore-deprecated"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><div class="moreinfo">More info: <a href="https://developer.android.com/kotlin/interop#nullability_annotations">https://developer.android.com/kotlin/interop#nullability_annotations</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeImplicitIntentLaunch<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This intent matches a non-exported component within the same app. In many cases, the app developer could instead use an explicit Intent to send messages to their internal components, ensuring that the messages are safely delivered without exposure to malicious apps on the device. Using such implicit intents will result in a crash in an upcoming version of Android.<br/><div class="moreinfo">More info: <a href="https://goo.gle/ImplicitIntentHijack">https://goo.gle/ImplicitIntentHijack</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsupportedChromeOsHardware<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>&lt;uses-feature></code> element should not require this unsupported large screen hardware feature. Any &lt;uses-feature> not explicitly marked with <code>required="false"</code> is necessary on the device to be installed on. Ensure that any features that might prevent it from being installed on a ChromeOS, large screen, or foldable device are reviewed and marked as not required in the manifest.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/arc/manifest.html#incompat-entries">https://developer.android.com/topic/arc/manifest.html#incompat-entries</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedIds<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This resource id definition appears not to be needed since it is not referenced from anywhere. Having id definitions, even if unused, is not necessarily a bad idea since they make working on layouts and menus easier, so there is not a strong reason to delete these.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ValidActionsXml<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that an actions XML file is properly formed<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">VulnerableCordovaVersion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The version of Cordova used in the app is vulnerable to security issues. Please update to the latest Apache Cordova version.<br/><div class="moreinfo">More info: <a href="https://cordova.apache.org/announcements/2015/11/20/security.html">https://cordova.apache.org/announcements/2015/11/20/security.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongThreadInterprocedural<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Searches for interprocedural call paths that violate thread annotations in the program. Tracks the flow of instantiated types and lambda expressions to increase accuracy across method boundaries.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/components/processes-and-threads.html#Threads">https://developer.android.com/guide/components/processes-and-threads.html#Threads</a>
</div><br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SuppressedIssuesLink" onclick="reveal('SuppressedIssues');">
List Missing Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingIssuesCardLink" onclick="hideid('MissingIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="SuppressInfo"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SuppressCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Suppressing Warnings and Errors</h2>
  </div>
              <div class="mdl-card__supporting-text">
Lint errors can be suppressed in a variety of ways:<br/>
<br/>
1. With a <code>@SuppressLint</code> annotation in the Java code<br/>
2. With a <code>tools:ignore</code> attribute in the XML file<br/>
3. With a //noinspection comment in the source code<br/>
4. With ignore flags specified in the <code>build.gradle</code> file, as explained below<br/>
5. With a <code>lint.xml</code> configuration file in the project<br/>
6. With a <code>lint.xml</code> configuration file passed to lint via the --config flag<br/>
7. With the --ignore flag passed to lint.<br/>
<br/>
To suppress a lint warning with an annotation, add a <code>@SuppressLint("id")</code> annotation on the class, method or variable declaration closest to the warning instance you want to disable. The id can be one or more issue id's, such as <code>"UnusedResources"</code> or <code>{"UnusedResources","UnusedIds"}</code>, or it can be <code>"all"</code> to suppress all lint warnings in the given scope.<br/>
<br/>
To suppress a lint warning with a comment, add a <code>//noinspection id</code> comment on the line before the statement with the error.<br/>
<br/>
To suppress a lint warning in an XML file, add a <code>tools:ignore="id"</code> attribute on the element containing the error, or one of its surrounding elements. You also need to define the namespace for the tools prefix on the root element in your document, next to the <code>xmlns:android</code> declaration:<br/>
<code>xmlns:tools="http://schemas.android.com/tools"</code><br/>
<br/>
To suppress a lint warning in a <code>build.gradle</code> file, add a section like this:<br/>

<pre>
android {
    lintOptions {
        disable 'TypographyFractions','TypographyQuotes'
    }
}
</pre>
<br/>
Here we specify a comma separated list of issue id's after the disable command. You can also use <code>warning</code> or <code>error</code> instead of <code>disable</code> to change the severity of issues.<br/>
<br/>
To suppress lint warnings with a configuration XML file, create a file named <code>lint.xml</code> and place it at the root directory of the module in which it applies.<br/>
<br/>
The format of the <code>lint.xml</code> file is something like the following:<br/>

<pre>
&lt;?xml version="1.0" encoding="UTF-8"?>
&lt;lint>
    &lt;!-- Ignore everything in the test source set -->
    &lt;issue id="all">
        &lt;ignore path="\*/test/\*" />
    &lt;/issue>

    &lt;!-- Disable this given check in this project -->
    &lt;issue id="IconMissingDensityFolder" severity="ignore" />

    &lt;!-- Ignore the ObsoleteLayoutParam issue in the given files -->
    &lt;issue id="ObsoleteLayoutParam">
        &lt;ignore path="res/layout/activation.xml" />
        &lt;ignore path="res/layout-xlarge/activation.xml" />
        &lt;ignore regexp="(foo|bar)\.java" />
    &lt;/issue>

    &lt;!-- Ignore the UselessLeaf issue in the given file -->
    &lt;issue id="UselessLeaf">
        &lt;ignore path="res/layout/main.xml" />
    &lt;/issue>

    &lt;!-- Change the severity of hardcoded strings to "error" -->
    &lt;issue id="HardcodedText" severity="error" />
&lt;/lint>
</pre>
<br/>
To suppress lint checks from the command line, pass the --ignore flag with a comma separated list of ids to be suppressed, such as:<br/>
<code>$ lint --ignore UnusedResources,UselessLeaf /my/project/path</code><br/>
<br/>
For more information, see <a href="https://developer.android.com/studio/write/lint.html#config">https://developer.android.com/studio/write/lint.html#config</a><br/>

            </div>
            </div>
          </section>    </div>
  </main>
</div>
</body>
</html>