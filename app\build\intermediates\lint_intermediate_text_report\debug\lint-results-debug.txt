C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_favorites.xml:72: Error: Corresponding method handler 'public void enterForum(android.view.View)' not found [OnClick]
        android:onClick="enterForum"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_home.xml:24: Error: Corresponding method handler 'public void BtBuy(android.view.View)' not found [OnClick]
        android:onClick="BtBuy"
        ~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "OnClick":
   The onClick attribute value should be the name of a method in this View's
   context to invoke when the view is clicked. This name must correspond to a
   public method that takes exactly one parameter of type View.

   Must be a string value, using '\;' to escape characters such as '\n' or
   '\uxxxx' for a unicode character.

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\build.gradle:46: Error: Version 28 (intended for Android Pie and below) is the last version of the legacy support library, so we recommend that you migrate to AndroidX libraries when using Android Q and moving forward. The IDE can help with this: Refactor > Migrate to AndroidX... [GradleCompatible]
    implementation 'com.android.support:appcompat-v7:28.0.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\build.gradle:49: Error: Version 28 (intended for Android Pie and below) is the last version of the legacy support library, so we recommend that you migrate to AndroidX libraries when using Android Q and moving forward. The IDE can help with this: Refactor > Migrate to AndroidX... [GradleCompatible]
    implementation 'com.android.support:cardview-v7:28.0.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\build.gradle:50: Error: Version 28 (intended for Android Pie and below) is the last version of the legacy support library, so we recommend that you migrate to AndroidX libraries when using Android Q and moving forward. The IDE can help with this: Refactor > Migrate to AndroidX... [GradleCompatible]
    implementation 'com.android.support:design:28.0.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "GradleCompatible":
   There are some combinations of libraries, or tools and libraries, that are
   incompatible, or can lead to bugs. One such incompatibility is compiling
   with a version of the Android support libraries that is not the latest
   version (or in particular, a version lower than your targetSdkVersion).

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_negtive.xml:22: Error: Class referenced in the layout file, androidx.cardview.widget.CardView, was not found in the project or the libraries [MissingClass]
    <androidx.cardview.widget.CardView
    ^
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_positive.xml:22: Error: Class referenced in the layout file, androidx.cardview.widget.CardView, was not found in the project or the libraries [MissingClass]
    <androidx.cardview.widget.CardView
    ^

   Explanation for issues of type "MissingClass":
   If a class is referenced in the manifest or in a layout file, it must also
   exist in the project (or in one of the libraries included by the project.
   This check helps uncover typos in registration names, or attempts to rename
   or move classes without updating the XML references properly.

   https://developer.android.com/guide/topics/manifest/manifest-intro.html

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\AndroidManifest.xml:4: Warning: WRITE_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to write to shared storage, use the MediaStore.createWriteRequest intent. [ScopedStorage]
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ScopedStorage":
   Scoped storage is enforced on Android 10+ (or Android 11+ if using
   requestLegacyExternalStorage). In particular, WRITE_EXTERNAL_STORAGE will
   no longer provide write access to all files; it will provide the equivalent
   of READ_EXTERNAL_STORAGE instead.

   As of Android 13, if you need to query or interact with MediaStore or media
   files on the shared storage, you should be using instead one or more new
   storage permissions:
   * android.permission.READ_MEDIA_IMAGES
   * android.permission.READ_MEDIA_VIDEO
   * android.permission.READ_MEDIA_AUDIO

   and then add maxSdkVersion="33" to the older permission. See the developer
   guide for how to do this:
   https://developer.android.com/about/versions/13/behavior-changes-13#granula
   r-media-permissions

   The MANAGE_EXTERNAL_STORAGE permission can be used to manage all files, but
   it is rarely necessary and most apps on Google Play are not allowed to use
   it. Most apps should instead migrate to use scoped storage. To modify or
   delete files, apps should request write access from the user as described
   at https://goo.gle/android-mediastore-createwriterequest.

   To learn more, read these resources: Play policy:
   https://goo.gle/policy-storage-help Allowable use cases:
   https://goo.gle/policy-storage-usecases

   https://goo.gle/android-storage-usecases

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\FavoritesFragment.java:104: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
                    nameSetting.edit().putString("name", nicknameStrig).commit();//getSharedPreferences()方法，呼叫edit()方法取得編輯器物件，
                                                                        ~~~~~~~~

   Explanation for issues of type "ApplySharedPref":
   Consider using apply() instead of commit on shared preferences. Whereas
   commit blocks and writes its data to persistent storage immediately, apply
   will handle it in the background.

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\build.gradle:13: Warning: Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details. [OldTargetApi]
        targetSdk 34
        ~~~~~~~~~~~~

   Explanation for issues of type "OldTargetApi":
   When your application runs on a version of Android that is more recent than
   your targetSdkVersion specifies that it has been tested with, various
   compatibility modes kick in. This ensures that your application continues
   to work, but it may look out of place. For example, if the targetSdkVersion
   is less than 14, your app may get an option button in the UI.

   To fix this issue, set the targetSdkVersion to the highest available value.
   Then test your app to make sure everything works correctly. You may want to
   consult the compatibility notes to see what changes apply to each version
   you are adding support for:
   https://developer.android.com/reference/android/os/Build.VERSION_CODES.html
   as well as follow this guide:
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\BuyMoney.java:115: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                        eName = cursor.getString(cursor.getColumnIndex("name"));
                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\BuyMoney.java:118: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                        eAmount = cursor.getString(cursor.getColumnIndex("amount"));
                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\BuyMoney.java:120: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                        ebuydate = cursor.getString(cursor.getColumnIndex("buydate"));
                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\BuyMoney.java:122: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                        ewherebuy = cursor.getString(cursor.getColumnIndex("wherebuy"));
                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\BuyMoney.java:124: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                        eID = cursor.getString(cursor.getColumnIndex("_id"));
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\BuyMoney.java:126: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                        ebuyprice=cursor.getString(cursor.getColumnIndex("buyprice"));
                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\FireForumListActivity2.java:134: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                 kind[i] =cur.getString(cur.getColumnIndex("finekind"));
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\PreVegetables.java:119: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                        txvKind = cursor.getString(cursor.getColumnIndex("kind"));
                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\PreVegetables.java:122: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                        txvFineKind = cursor.getString(cursor.getColumnIndex("finekind"));
                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\PreVegetables.java:123: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                        ebuydate = cursor.getString(cursor.getColumnIndex("buydate"));
                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\PreVegetables.java:124: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                        esavedate = cursor.getString(cursor.getColumnIndex("savedate"));
                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\PreVegetables.java:125: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                        eID = cursor.getString(cursor.getColumnIndex("_id"));
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\PreVegetables.java:126: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                        txvPart= cursor.getString(cursor.getColumnIndex("part"));
                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\VGanalysis.java:118: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                txvKind = cursor.getString(cursor.getColumnIndex("kind"));
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\VGanalysis.java:121: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                txvFineKind = cursor.getString(cursor.getColumnIndex("finekind"));
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\VGanalysis.java:122: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                ebuydate = cursor.getString(cursor.getColumnIndex("buydate"));
                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\VGanalysis.java:123: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                esavedate = cursor.getString(cursor.getColumnIndex("savedate"));
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\VGanalysis.java:124: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                eID = cursor.getString(cursor.getColumnIndex("_id"));
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\VGanalysis.java:125: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                txvPart= cursor.getString(cursor.getColumnIndex("part"));
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Range":
   Some parameters are required to in a particular numerical range; this check
   makes sure that arguments passed fall within the range. For arrays, Strings
   and collections this refers to the size or length.

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\BuyActivity.java:514: Warning: Toast created but not shown: did you forget to call show()? [ShowToast]
            Toast.makeText(this, "無法取得照片", Toast.LENGTH_LONG);
            ~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\FragmentList.java:37: Warning: Toast created but not shown: did you forget to call show()? [ShowToast]
        Toast.makeText(getActivity(),"Item"+position,Toast.LENGTH_LONG);
        ~~~~~~~~~~~~~~

   Explanation for issues of type "ShowToast":
   Toast.makeText() creates a Toast but does not show it. You must call show()
   on the resulting object to actually make the Toast appear.

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\FireDiscActivity2.java:135: Warning: To get local formatting use getDateInstance(), getDateTimeInstance(), or getTimeInstance(), or use new SimpleDateFormat(String template, Locale locale) with for example Locale.US for ASCII dates. [SimpleDateFormat]
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("MM/dd HH:mm");
                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\FireForumListActivity2.java:272: Warning: To get local formatting use getDateInstance(), getDateTimeInstance(), or getTimeInstance(), or use new SimpleDateFormat(String template, Locale locale) with for example Locale.US for ASCII dates. [SimpleDateFormat]
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("MM/dd HH:mm");
                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SimpleDateFormat":
   Almost all callers should use getDateInstance(), getDateTimeInstance(), or
   getTimeInstance() to get a ready-made instance of SimpleDateFormat suitable
   for the user's locale. The main reason you'd create an instance this class
   directly is because you need to format/parse a specific machine-readable
   format, in which case you almost certainly want to explicitly ask for US to
   ensure that you get ASCII digits (rather than, say, Arabic digits).

   Therefore, you should either use the form of the SimpleDateFormat
   constructor where you pass in an explicit locale, such as Locale.US, or use
   one of the get instance methods, or suppress this error if really know what
   you are doing.

   https://developer.android.com/reference/java/text/SimpleDateFormat.html

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\FireDiscActivity2.java:131: Warning: Avoid passing null as the view root (needed to resolve layout parameters on the inflated layout's root element) [InflateParams]
            View myView = mInflater.inflate(R.layout.disc_content2,null);
                                                                   ~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\FireForumListActivity2.java:258: Warning: Avoid passing null as the view root (needed to resolve layout parameters on the inflated layout's root element) [InflateParams]
                itemlayout = (LinearLayout) mInflater.inflate(R.layout.forum_listitem2, null);  // 決定XML
                                                                                        ~~~~

   Explanation for issues of type "InflateParams":
   When inflating a layout, avoid passing in null as the parent view, since
   otherwise any layout parameters on the root of the inflated layout will be
   ignored.

   https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\build.gradle:47: Warning: A newer version of com.android.support.constraint:constraint-layout than 1.1.3 is available: 2.0.4 [GradleDependency]
    implementation 'com.android.support.constraint:constraint-layout:1.1.3'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\BuyActivity.java:101: Warning: You should not lock orientation of your activities, so that you can support a good user experience for any device or orientation [SourceLockedOrientationActivity]
        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SourceLockedOrientationActivity":
   The Activity should not be locked to a portrait orientation so that users
   can take advantage of the multi-window environments and larger
   landscape-first screens that Android runs on such as ChromeOS, tablets, and
   foldables. To fix the issue, consider calling setRequestedOrientation with
   the ActivityInfo.SCREEN_ORIENTATION_FULL_SENSOR or
   ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED options or removing the call
   all together.

   https://developer.android.com/guide/topics/large-screens/large-screen-cookbook#restricted_app_orientation

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_positive.xml:50: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
                    android:textSize="25dp"
                    ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_positive.xml:70: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
                    android:textSize="18dp"
                    ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_positive.xml:80: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
                    android:textSize="20dp"
                    ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\forum_listitem2.xml:63: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
            android:textSize="22dp"></TextView>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item.xml:28: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
            android:textSize="20dip" />
            ~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SpUsage":
   When setting text sizes, you should normally use sp, or "scale-independent
   pixels". This is like the dp unit, but it is also scaled by the user's font
   size preference. It is recommend you use this unit when specifying font
   sizes, so they will be adjusted for both the screen density and the user's
   preference.

   There are cases where you might need to use dp; typically this happens when
   the text is in a container with a specific dp-size. This will prevent the
   text from spilling outside the container. Note however that this means that
   the user's font size settings are not respected, so consider adjusting the
   layout itself to be more flexible.

   https://developer.android.com/training/multiscreen/screendensities.html

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\values\strings.xml:50: Warning: "Wanna" is a common misspelling; did you mean "Want to"? [Typos]
    <string name="negative_popup_text_defaulte">You have failed to complete the challenge. So close! Wanna have another go at it?</string>
                                                                                                     ^

   Explanation for issues of type "Typos":
   This check looks through the string definitions, and if it finds any words
   that look like likely misspellings, they are flagged.

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\BuyMoney.java:91: Warning: This Cursor should be freed up after use with #close() [Recycle]
                Cursor cursor = db.rawQuery("SELECT * FROM "+ TB_NAME,null);
                                   ~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\PreVegetables.java:95: Warning: This Cursor should be freed up after use with #close() [Recycle]
                Cursor cursor = db.rawQuery("SELECT * FROM "+ TB_NAME,null);
                                   ~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\VGanalysis.java:89: Warning: This Cursor should be freed up after use with #close() [Recycle]
        Cursor cursor = db.rawQuery("SELECT * FROM hotlist GROUP BY finekind HAVING count(*)>0 ",null);
                           ~~~~~~~~

   Explanation for issues of type "Recycle":
   Many resources, such as TypedArrays, VelocityTrackers, etc., should be
   recycled (with a recycle() call) after use. This lint check looks for
   missing recycle() calls.

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\FireDiscActivity2.java:131: Warning: Unconditional layout inflation from view adapter: Should use View Holder pattern (use recycled view passed into this method as the second parameter) for smoother scrolling [ViewHolder]
            View myView = mInflater.inflate(R.layout.disc_content2,null);
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ViewHolder":
   When implementing a view Adapter, you should avoid unconditionally
   inflating a new layout; if an available item is passed in for reuse, you
   should try to use that one instead. This helps make for example ListView
   scrolling much smoother.

   https://developer.android.com/guide/topics/ui/layout/recyclerview#ViewHolder

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\FireForumListActivity2.java:72: Warning: This Handler class should be static or leaks might occur (anonymous android.os.Handler) [HandlerLeak]
    private Handler handler = new Handler() {  //~~~~~~~~~建立Handler物件才能使用handleMessage(Message msg)方法，
                              ^
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\WelcomeActivty.java:25: Warning: This Handler class should be static or leaks might occur (anonymous android.os.Handler) [HandlerLeak]
    private Handler mHandler = new Handler() {
                               ^

   Explanation for issues of type "HandlerLeak":
   Since this Handler is declared as an inner class, it may prevent the outer
   class from being garbage collected. If the Handler is using a Looper or
   MessageQueue for a thread other than the main thread, then there is no
   issue. If the Handler is using the Looper or MessageQueue of the main
   thread, you need to fix your Handler declaration, as follows: Declare the
   Handler as a static class; In the outer class, instantiate a WeakReference
   to the outer class and pass this object to your Handler when you
   instantiate the Handler; Make all references to members of the outer class
   using the WeakReference object.

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item2money.xml:2: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
 ~~~~~~~~~~~~

   Explanation for issues of type "DisableBaselineAlignment":
   When a LinearLayout is used to distribute the space proportionally between
   nested layouts, the baseline alignment property should be turned off to
   make the layout computation faster.

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:98: Warning: Use a layout_width of 0dp instead of 256dp for better performance [InefficientWeight]
                android:layout_width="256dp"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:110: Warning: Use a layout_height of 0dp instead of 50dp for better performance [InefficientWeight]
            android:layout_height="50dp"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:133: Warning: Use a layout_width of 0dp instead of 256dp for better performance [InefficientWeight]
                android:layout_width="256dp"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:97: Warning: Use a layout_width of 0dp instead of wrap_content for better performance [InefficientWeight]
                android:layout_width="wrap_content"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:119: Warning: Use a layout_width of 0dp instead of wrap_content for better performance [InefficientWeight]
                android:layout_width="wrap_content"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item1.xml:133: Warning: Use a layout_height of 0dp instead of match_parent for better performance [InefficientWeight]
            android:layout_height="match_parent"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "InefficientWeight":
   When only a single widget in a LinearLayout defines a weight, it is more
   efficient to assign a width/height of 0dp to it since it will absorb all
   the remaining space anyway. With a declared width/height of 0dp it does not
   have to measure its own size first.

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:135: Warning: Nested weights are bad for performance [NestedWeights]
                android:layout_weight="1"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item1.xml:134: Warning: Nested weights are bad for performance [NestedWeights]
            android:layout_weight="1"
            ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item2money.xml:126: Warning: Nested weights are bad for performance [NestedWeights]
            android:layout_weight="0.5"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NestedWeights":
   Layout weights require a widget to be measured twice. When a LinearLayout
   with non-zero weights is nested inside another LinearLayout with non-zero
   weights, then the number of measurements increase exponentially.

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_main.xml:7: Warning: Possible overdraw: Root element paints background @android:color/holo_red_dark with a theme that also paints a background (inferred theme is @style/AppTheme) [Overdraw]
    android:background="@android:color/holo_red_dark"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc2.xml:7: Warning: Possible overdraw: Root element paints background #ff85ff with a theme that also paints a background (inferred theme is @style/AppTheme) [Overdraw]
    android:background="#ff85ff"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc_content2.xml:6: Warning: Possible overdraw: Root element paints background #ffffff with a theme that also paints a background (inferred theme is @style/AppTheme) [Overdraw]
    android:background="#ffffff"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fireforulist2.xml:7: Warning: Possible overdraw: Root element paints background #ffd47a with a theme that also paints a background (inferred theme is @style/AppTheme) [Overdraw]
    android:background="#ffd47a"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\forum_listitem2.xml:7: Warning: Possible overdraw: Root element paints background #ffffff with a theme that also paints a background (inferred theme is @style/AppTheme) [Overdraw]
    android:background="#ffffff"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_favorites.xml:7: Warning: Possible overdraw: Root element paints background #fffc7a with a theme that also paints a background (inferred theme is @style/AppTheme) [Overdraw]
    android:background="#fffc7a"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_home.xml:7: Warning: Possible overdraw: Root element paints background #009688 with a theme that also paints a background (inferred theme is @style/AppTheme) [Overdraw]
    android:background="#009688">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:6: Warning: Possible overdraw: Root element paints background @android:color/holo_green_light with a theme that also paints a background (inferred theme is @style/AppTheme) [Overdraw]
    android:background="@android:color/holo_green_light">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\drawable\cookbear.jpg: Warning: The resource R.drawable.cookbear appears to be unused [UnusedResources]
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\drawable\ic_favorite_black_24dp.xml:1: Warning: The resource R.drawable.ic_favorite_black_24dp appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\drawable\ic_launch_black_24dp.xml:1: Warning: The resource R.drawable.ic_launch_black_24dp appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml:2: Warning: The resource R.mipmap.ic_launcher appears to be unused [UnusedResources]
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item.xml:8: Warning: The resource R.layout.item appears to be unused [UnusedResources]
    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    ^
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\drawable\speech_bubble.png: Warning: The resource R.drawable.speech_bubble appears to be unused [UnusedResources]
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\values\strings.xml:2: Warning: The resource R.string.app_name appears to be unused [UnusedResources]
    <string name="app_name">MyVG0809</string>
            ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\values\strings.xml:30: Warning: The resource R.array.weekday appears to be unused [UnusedResources]
    <string-array name="weekday">
                  ~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:60: Warning: This LinearLayout view is unnecessary (no children, no background, no id, no style) [UselessLeaf]
            <LinearLayout
             ~~~~~~~~~~~~

   Explanation for issues of type "UselessLeaf":
   A layout that has no children or no background can often be removed (since
   it is invisible) for a flatter and more efficient layout hierarchy.

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buymoney.xml:9: Warning: This LinearLayout layout or its RelativeLayout parent is unnecessary [UselessParent]
    <LinearLayout
     ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_pre_vegetables.xml:9: Warning: This LinearLayout layout or its RelativeLayout parent is unnecessary [UselessParent]
    <LinearLayout
     ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_vganalysis.xml:9: Warning: This LinearLayout layout or its RelativeLayout parent is unnecessary [UselessParent]
    <LinearLayout
     ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_negtive.xml:36: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
            <LinearLayout
             ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_positive.xml:36: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
            <LinearLayout
             ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\forum_listitem2.xml:10: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary; transfer the background attribute to the other view [UselessParent]
    <LinearLayout
     ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:8: Warning: This LinearLayout layout or its RelativeLayout parent is unnecessary; transfer the background attribute to the other view [UselessParent]
    <LinearLayout
     ~~~~~~~~~~~~

   Explanation for issues of type "UselessParent":
   A layout with children that has no siblings, is not a scrollview or a root
   layout, and does not have a background, can be removed and have its
   children moved directly into the parent for a flatter and more efficient
   layout hierarchy.

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml:2: Warning: The application adaptive roundIcon is missing a monochrome tag [MonochromeLauncherIcon]
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
^

   Explanation for issues of type "MonochromeLauncherIcon":
   If android:roundIcon and android:icon are both in your manifest, you must
   either remove the reference to android:roundIcon if it is not needed; or,
   supply the monochrome icon in the drawable defined by the android:roundIcon
   and android:icon attribute.

   For example, if android:roundIcon and android:icon are both in the
   manifest, a launcher might choose to use android:roundIcon over
   android:icon to display the adaptive app icon. Therefore, your themed
   application iconwill not show if your monochrome attribute is not also
   specified in android:roundIcon.

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\drawable\bill.jpg: Warning: Found bitmap drawable res/drawable/bill.jpg in densityless folder [IconLocation]
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\drawable\cookbear.jpg: Warning: Found bitmap drawable res/drawable/cookbear.jpg in densityless folder [IconLocation]
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\drawable\cookbear2.jpg: Warning: Found bitmap drawable res/drawable/cookbear2.jpg in densityless folder [IconLocation]
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\drawable\meat.jpg: Warning: Found bitmap drawable res/drawable/meat.jpg in densityless folder [IconLocation]
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\drawable\seafood.jpg: Warning: Found bitmap drawable res/drawable/seafood.jpg in densityless folder [IconLocation]
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\drawable\speech_bubble.png: Warning: Found bitmap drawable res/drawable/speech_bubble.png in densityless folder [IconLocation]
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\drawable\takepic.png: Warning: Found bitmap drawable res/drawable/takepic.png in densityless folder [IconLocation]
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\drawable\vege1.jpg: Warning: Found bitmap drawable res/drawable/vege1.jpg in densityless folder [IconLocation]
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\drawable\vegetable.jpg: Warning: Found bitmap drawable res/drawable/vegetable.jpg in densityless folder [IconLocation]
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\drawable\welcom.jpg: Warning: Found bitmap drawable res/drawable/welcom.jpg in densityless folder [IconLocation]

   Explanation for issues of type "IconLocation":
   The res/drawable folder is intended for density-independent graphics such
   as shapes defined in XML. For bitmaps, move it to drawable-mdpi and
   consider providing higher and lower resolution versions in drawable-ldpi,
   drawable-hdpi and drawable-xhdpi. If the icon really is density independent
   (for example a solid color) you can place it in drawable-nodpi.

   https://developer.android.com/guide/practices/screens_support.html

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:22: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:29: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:36: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:43: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:331: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:338: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~

   Explanation for issues of type "ButtonStyle":
   Button bars typically use a borderless style for the buttons. Set the
   style="?android:attr/buttonBarButtonStyle" attribute on each of the
   buttons, and set style="?android:attr/buttonBarStyle" on the parent layout

   https://material.io/components/dialogs/

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:313: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc2.xml:24: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_favorites.xml:22: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:95: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:117: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~

   Explanation for issues of type "Autofill":
   Specify an autofillHints attribute when targeting SDK version 26 or higher
   or explicitly specify that the view is not important for autofill. Your app
   can help an autofill service classify the data correctly by providing the
   meaning of each view that could be autofillable, such as views representing
   usernames, passwords, credit card fields, email addresses, etc.

   The hints can have any value, but it is recommended to use predefined
   values like 'username' for a username or 'creditCardNumber' for a credit
   card number. For a list of all predefined autofill hint constants, see the
   AUTOFILL_HINT_ constants in the View reference at
   https://developer.android.com/reference/android/view/View.html.

   You can mark a view unimportant for autofill by specifying an
   importantForAutofill attribute on that view or a parent view. See
   https://developer.android.com/reference/android/view/View.html#setImportant
   ForAutofill(int).

   https://developer.android.com/guide/topics/text/autofill.html

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:78: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:115: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buymoney.xml:42: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_vganalysis.xml:42: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageButton
             ~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_welcome.xml:9: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_negtive.xml:18: Warning: Empty contentDescription attribute on image [ContentDescription]
        android:contentDescription="TODO"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_negtive.xml:62: Warning: Empty contentDescription attribute on image [ContentDescription]
                    android:contentDescription="TODO"></ImageView>
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_positive.xml:19: Warning: Empty contentDescription attribute on image [ContentDescription]
        android:contentDescription="TODO"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_positive.xml:54: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\forum_listitem2.xml:15: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_favorites.xml:38: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:27: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:49: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:71: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:104: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:126: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item.xml:11: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView android:id="@+id/photo"
         ~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item1.xml:25: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:313: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_favorites.xml:22: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
    <EditText
     ~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:95: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:117: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~

   Explanation for issues of type "LabelFor":
   Editable text fields should provide an android:hint or, provided your
   minSdkVersion is at least 17, they may be referenced by a view with a
   android:labelFor attribute.

   When using android:labelFor, be sure to provide an android:text or an
   android:contentDescription.

   If your view is labeled but by a label in a different layout which includes
   this one, just suppress this warning from lint.

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\BuyActivity.java:128: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        buydate.setText(Integer.toString(thisYear)+"/" + Integer.toString(thisMonth)+"/" + Integer.toString(thisDate));//日期用
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\BuyActivity.java:128: Warning: Number formatting does not take into account locale settings. Consider using String.format instead. [SetTextI18n]
        buydate.setText(Integer.toString(thisYear)+"/" + Integer.toString(thisMonth)+"/" + Integer.toString(thisDate));//日期用
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\BuyActivity.java:128: Warning: Number formatting does not take into account locale settings. Consider using String.format instead. [SetTextI18n]
        buydate.setText(Integer.toString(thisYear)+"/" + Integer.toString(thisMonth)+"/" + Integer.toString(thisDate));//日期用
                                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\BuyActivity.java:128: Warning: Number formatting does not take into account locale settings. Consider using String.format instead. [SetTextI18n]
        buydate.setText(Integer.toString(thisYear)+"/" + Integer.toString(thisMonth)+"/" + Integer.toString(thisDate));//日期用
                                                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\BuyActivity.java:557: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            buydate.setText(( year + "/" + (month + 1) + "/" + dayOfMonth));
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\java\com\example\bottomnavigationview\BuyActivity.java:557: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            buydate.setText(( year + "/" + (month + 1) + "/" + dayOfMonth));
                                            ~~~~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:27: Warning: Hardcoded string "首頁", should use @string resource [HardcodedText]
                android:text="首頁" />
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:34: Warning: Hardcoded string "總清單", should use @string resource [HardcodedText]
                android:text="總清單" />
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:41: Warning: Hardcoded string "菜價分析", should use @string resource [HardcodedText]
                android:text="菜價分析" />
                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:48: Warning: Hardcoded string "保存期限", should use @string resource [HardcodedText]
                android:text="保存期限" />
                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:93: Warning: Hardcoded string "購買日期:", should use @string resource [HardcodedText]
                android:text="購買日期:"
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:102: Warning: Hardcoded string "2019-10-08", should use @string resource [HardcodedText]
                android:text="2019-10-08"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:128: Warning: Hardcoded string "保存期期:", should use @string resource [HardcodedText]
                android:text="保存期期:"
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:137: Warning: Hardcoded string "2019-10-15", should use @string resource [HardcodedText]
                android:text="2019-10-15"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:154: Warning: Hardcoded string "種類", should use @string resource [HardcodedText]
                android:text="種類"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:185: Warning: Hardcoded string "子類", should use @string resource [HardcodedText]
                android:text="子類"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:216: Warning: Hardcoded string "部位", should use @string resource [HardcodedText]
                android:text="部位"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:245: Warning: Hardcoded string "份數", should use @string resource [HardcodedText]
                android:text="份數"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:276: Warning: Hardcoded string "位置", should use @string resource [HardcodedText]
                android:text="位置"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:309: Warning: Hardcoded string "備註:", should use @string resource [HardcodedText]
                android:text="備註:"
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:336: Warning: Hardcoded string "儲存", should use @string resource [HardcodedText]
                android:text="儲存" />
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:343: Warning: Hardcoded string "再記一筆", should use @string resource [HardcodedText]
                android:text="再記一筆" />
                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:351: Warning: Hardcoded string "Button", should use @string resource [HardcodedText]
            android:text="Button" />
            ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buymoney.xml:20: Warning: Hardcoded string "歷史菜價", should use @string resource [HardcodedText]
            android:text="歷史菜價"
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buymoney.xml:33: Warning: Hardcoded string "價格高至低", should use @string resource [HardcodedText]
                android:text="價格高至低" />
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buymoney.xml:40: Warning: Hardcoded string "價格低至高", should use @string resource [HardcodedText]
                android:text="價格低至高" />
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_pre_vegetables.xml:20: Warning: Hardcoded string "總清單", should use @string resource [HardcodedText]
            android:text="總清單"
            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_vganalysis.xml:20: Warning: Hardcoded string "保存提醒", should use @string resource [HardcodedText]
            android:text="保存提醒"
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_vganalysis.xml:33: Warning: Hardcoded string "快到期", should use @string resource [HardcodedText]
                android:text="快到期" />
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_vganalysis.xml:40: Warning: Hardcoded string "已到期", should use @string resource [HardcodedText]
                android:text="已到期" />
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\menu\bottom_navigation.xml:6: Warning: Hardcoded string "主頁", should use @string resource [HardcodedText]
        android:title="主頁" />
        ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\menu\bottom_navigation.xml:10: Warning: Hardcoded string "推薦食譜", should use @string resource [HardcodedText]
        android:title="推薦食譜" />
        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\menu\bottom_navigation.xml:14: Warning: Hardcoded string "個人", should use @string resource [HardcodedText]
        android:title="個人" />
        ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc2.xml:19: Warning: Hardcoded string "TextView", should use @string resource [HardcodedText]
        android:text="TextView"
        ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc2.xml:34: Warning: Hardcoded string "請輸入留言", should use @string resource [HardcodedText]
        android:hint="請輸入留言"
        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc2.xml:49: Warning: Hardcoded string "送出", should use @string resource [HardcodedText]
        android:text="送出"
        ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc2.xml:73: Warning: Hardcoded string "TextView", should use @string resource [HardcodedText]
        android:text="TextView"
        ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc2.xml:83: Warning: Hardcoded string "說:", should use @string resource [HardcodedText]
        android:text="說:"
        ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc_content2.xml:17: Warning: Hardcoded string "TextView", should use @string resource [HardcodedText]
        android:text="TextView"
        ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc_content2.xml:28: Warning: Hardcoded string "TextView", should use @string resource [HardcodedText]
        android:text="TextView"
        ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc_content2.xml:40: Warning: Hardcoded string "留言者", should use @string resource [HardcodedText]
        android:text="留言者"
        ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc_content2.xml:50: Warning: Hardcoded string "2018/01/01", should use @string resource [HardcodedText]
        android:text="2018/01/01"
        ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc_content2.xml:61: Warning: Hardcoded string "留言時間:", should use @string resource [HardcodedText]
        android:text="留言時間:"
        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_negtive.xml:18: Warning: Hardcoded string "TODO", should use @string resource [HardcodedText]
        android:contentDescription="TODO"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_negtive.xml:62: Warning: Hardcoded string "TODO", should use @string resource [HardcodedText]
                    android:contentDescription="TODO"></ImageView>
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_negtive.xml:83: Warning: Hardcoded string "Try Again", should use @string resource [HardcodedText]
                    android:text="Try Again"
                    ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_positive.xml:19: Warning: Hardcoded string "TODO", should use @string resource [HardcodedText]
        android:contentDescription="TODO"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_positive.xml:82: Warning: Hardcoded string "+57 points", should use @string resource [HardcodedText]
                    android:text="+57 points"></TextView>
                    ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_positive.xml:89: Warning: Hardcoded string "YESSSSSS", should use @string resource [HardcodedText]
                    android:text="YESSSSSS"
                    ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fireforulist2.xml:16: Warning: Hardcoded string "Refresh", should use @string resource [HardcodedText]
        android:text="Refresh"
        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fireforulist2.xml:43: Warning: Hardcoded string "食譜專區", should use @string resource [HardcodedText]
        android:text="食譜專區"
        ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\forum_listitem2.xml:32: Warning: Hardcoded string "2018/01/01", should use @string resource [HardcodedText]
                android:text="2018/01/01"></TextView>
                ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\forum_listitem2.xml:40: Warning: Hardcoded string "TextView", should use @string resource [HardcodedText]
                android:text="TextView"></TextView>
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\forum_listitem2.xml:47: Warning: Hardcoded string "最後更新:", should use @string resource [HardcodedText]
                android:text="最後更新:"></TextView>
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\forum_listitem2.xml:55: Warning: Hardcoded string "推薦給", should use @string resource [HardcodedText]
                android:text="推薦給"></TextView>
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\forum_listitem2.xml:62: Warning: Hardcoded string "Subject", should use @string resource [HardcodedText]
            android:text="Subject"
            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_favorites.xml:17: Warning: Hardcoded string "請輸入暱稱", should use @string resource [HardcodedText]
        android:text="請輸入暱稱"
        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_favorites.xml:58: Warning: Hardcoded string "準備中", should use @string resource [HardcodedText]
        android:text="準備中"
        ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_favorites.xml:73: Warning: Hardcoded string "食譜推薦區", should use @string resource [HardcodedText]
        android:text="食譜推薦區"
        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_home.xml:25: Warning: Hardcoded string "買菜", should use @string resource [HardcodedText]
        android:text="買菜"
        ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_home.xml:49: Warning: Hardcoded string "總清單", should use @string resource [HardcodedText]
        android:text="總清單"
        ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_home.xml:73: Warning: Hardcoded string "到期食材", should use @string resource [HardcodedText]
        android:text="到期食材"
        ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_home.xml:97: Warning: Hardcoded string "菜價分析", should use @string resource [HardcodedText]
        android:text="菜價分析"
        ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:19: Warning: Hardcoded string "個人選項", should use @string resource [HardcodedText]
            android:text="個人選項"
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:40: Warning: Hardcoded string "     個人化食譜選項", should use @string resource [HardcodedText]
                android:text="     個人化食譜選項"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:62: Warning: Hardcoded string "     個人資料", should use @string resource [HardcodedText]
                android:text="     個人資料"
                ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:84: Warning: Hardcoded string "     關於我們", should use @string resource [HardcodedText]
                android:text="     關於我們"
                ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:102: Warning: Hardcoded string "請輸入信箱", should use @string resource [HardcodedText]
                android:text="請輸入信箱" />
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:124: Warning: Hardcoded string "請輸入電話", should use @string resource [HardcodedText]
                android:text="請輸入電話" />
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:140: Warning: Hardcoded string "請輸入您的寶貴意見", should use @string resource [HardcodedText]
                android:hint="請輸入您的寶貴意見" />
                ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_search.xml:149: Warning: Hardcoded string "送出", should use @string resource [HardcodedText]
                android:text="送出"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item1.xml:19: Warning: Hardcoded string "TextView", should use @string resource [HardcodedText]
            android:text="TextView"
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item1.xml:52: Warning: Hardcoded string "種類：", should use @string resource [HardcodedText]
                android:text="種類：" />
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item1.xml:58: Warning: Hardcoded string "TextView", should use @string resource [HardcodedText]
                android:text="TextView"
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item1.xml:71: Warning: Hardcoded string "子類：", should use @string resource [HardcodedText]
                android:text="子類：" />
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item1.xml:77: Warning: Hardcoded string "TextView", should use @string resource [HardcodedText]
                android:text="TextView"
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item1.xml:91: Warning: Hardcoded string "部位：", should use @string resource [HardcodedText]
                android:text="部位：" />
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item1.xml:97: Warning: Hardcoded string "TextView", should use @string resource [HardcodedText]
                android:text="TextView"
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item1.xml:118: Warning: Hardcoded string "購買日期：", should use @string resource [HardcodedText]
                android:text="購買日期：" />
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item1.xml:126: Warning: Hardcoded string "TextView", should use @string resource [HardcodedText]
            android:text="TextView"
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item2money.xml:19: Warning: Hardcoded string "TextView", should use @string resource [HardcodedText]
            android:text="TextView"
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item2money.xml:43: Warning: Hardcoded string "種類：", should use @string resource [HardcodedText]
                android:text="種類：" />
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item2money.xml:49: Warning: Hardcoded string "TextView", should use @string resource [HardcodedText]
                android:text="TextView"
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item2money.xml:62: Warning: Hardcoded string "數量：", should use @string resource [HardcodedText]
                android:text="數量：" />
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item2money.xml:68: Warning: Hardcoded string "TextView", should use @string resource [HardcodedText]
                android:text="TextView"
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item2money.xml:82: Warning: Hardcoded string "購買地點：", should use @string resource [HardcodedText]
                android:text="購買地點：" />
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item2money.xml:88: Warning: Hardcoded string "TextView", should use @string resource [HardcodedText]
                android:text="TextView"
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item2money.xml:101: Warning: Hardcoded string "購買日期：", should use @string resource [HardcodedText]
                android:text="購買日期：" />
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item2money.xml:107: Warning: Hardcoded string "TextView", should use @string resource [HardcodedText]
                android:text="TextView"
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item2money.xml:137: Warning: Hardcoded string "元", should use @string resource [HardcodedText]
            android:text="元"
            ~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:83: Warning: Redundant attribute layout_marginLeft; already defining layout_marginStart with targetSdkVersion 34 [RtlHardcoded]
                android:layout_marginLeft="16dp"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:119: Warning: Consider replacing android:layout_marginLeft with android:layout_marginStart="25dp" to better support right-to-left layouts [RtlHardcoded]
                android:layout_marginLeft="25dp"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:121: Warning: Consider replacing android:layout_marginRight with android:layout_marginEnd="10dp" to better support right-to-left layouts [RtlHardcoded]
                android:layout_marginRight="10dp"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\activity_buy.xml:317: Warning: Consider replacing android:layout_marginRight with android:layout_marginEnd="10dp" to better support right-to-left layouts [RtlHardcoded]
                android:layout_marginRight="10dp"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc2.xml:15: Warning: Redundant attribute layout_marginLeft; already defining layout_marginStart with targetSdkVersion 34 [RtlHardcoded]
        android:layout_marginLeft="16dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc2.xml:16: Warning: Redundant attribute layout_marginRight; already defining layout_marginEnd with targetSdkVersion 34 [RtlHardcoded]
        android:layout_marginRight="8dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc2.xml:29: Warning: Redundant attribute layout_marginLeft; already defining layout_marginStart with targetSdkVersion 34 [RtlHardcoded]
        android:layout_marginLeft="16dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc2.xml:30: Warning: Redundant attribute layout_marginRight; already defining layout_marginEnd with targetSdkVersion 34 [RtlHardcoded]
        android:layout_marginRight="8dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc2.xml:47: Warning: Redundant attribute layout_marginRight; already defining layout_marginEnd with targetSdkVersion 34 [RtlHardcoded]
        android:layout_marginRight="16dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc2.xml:81: Warning: Redundant attribute layout_marginLeft; already defining layout_marginStart with targetSdkVersion 34 [RtlHardcoded]
        android:layout_marginLeft="8dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc_content2.xml:14: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
        android:gravity="left"
                         ~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc_content2.xml:26: Warning: Redundant attribute layout_marginLeft; already defining layout_marginStart with targetSdkVersion 34 [RtlHardcoded]
        android:layout_marginLeft="8dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc_content2.xml:37: Warning: Redundant attribute layout_marginLeft; already defining layout_marginStart with targetSdkVersion 34 [RtlHardcoded]
        android:layout_marginLeft="8dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc_content2.xml:48: Warning: Redundant attribute layout_marginLeft; already defining layout_marginStart with targetSdkVersion 34 [RtlHardcoded]
        android:layout_marginLeft="8dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\disc_content2.xml:59: Warning: Redundant attribute layout_marginLeft; already defining layout_marginStart with targetSdkVersion 34 [RtlHardcoded]
        android:layout_marginLeft="8dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_negtive.xml:15: Warning: Consider replacing android:layout_alignParentRight with android:layout_alignParentEnd="true" to better support right-to-left layouts [RtlHardcoded]
        android:layout_alignParentRight="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_negtive.xml:17: Warning: Consider replacing android:layout_marginRight with android:layout_marginEnd="7dp" to better support right-to-left layouts [RtlHardcoded]
        android:layout_marginRight="7dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_negtive.xml:28: Warning: Redundant attribute layout_alignParentLeft; already defining layout_alignParentStart with targetSdkVersion 34 [RtlHardcoded]
        android:layout_alignParentLeft="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_positive.xml:15: Warning: Redundant attribute layout_marginRight; already defining layout_marginEnd with targetSdkVersion 34 [RtlHardcoded]
        android:layout_marginRight="7dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_positive.xml:18: Warning: Consider replacing android:layout_alignParentRight with android:layout_alignParentEnd="true" to better support right-to-left layouts [RtlHardcoded]
        android:layout_alignParentRight="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\epic_popup_positive.xml:28: Warning: Redundant attribute layout_alignParentLeft; already defining layout_alignParentStart with targetSdkVersion 34 [RtlHardcoded]
        android:layout_alignParentLeft="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\forum_listitem2.xml:31: Warning: Redundant attribute layout_marginLeft; already defining layout_marginStart with targetSdkVersion 34 [RtlHardcoded]
                android:layout_marginLeft="8dp"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\forum_listitem2.xml:39: Warning: Redundant attribute layout_marginLeft; already defining layout_marginStart with targetSdkVersion 34 [RtlHardcoded]
                android:layout_marginLeft="8dp"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\forum_listitem2.xml:54: Warning: Redundant attribute layout_marginLeft; already defining layout_marginStart with targetSdkVersion 34 [RtlHardcoded]
                android:layout_marginLeft="8dp"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_favorites.xml:15: Warning: Redundant attribute layout_marginRight; already defining layout_marginEnd with targetSdkVersion 34 [RtlHardcoded]
        android:layout_marginRight="8dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_favorites.xml:71: Warning: Redundant attribute layout_marginRight; already defining layout_marginEnd with targetSdkVersion 34 [RtlHardcoded]
        android:layout_marginRight="8dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_home.xml:14: Warning: Redundant attribute layout_alignParentLeft; already defining layout_alignParentStart with targetSdkVersion 34 [RtlHardcoded]
        android:layout_alignParentLeft="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_home.xml:17: Warning: Redundant attribute layout_alignParentRight; already defining layout_alignParentEnd with targetSdkVersion 34 [RtlHardcoded]
        android:layout_alignParentRight="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_home.xml:39: Warning: Redundant attribute layout_alignParentLeft; already defining layout_alignParentStart with targetSdkVersion 34 [RtlHardcoded]
        android:layout_alignParentLeft="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_home.xml:42: Warning: Redundant attribute layout_alignParentRight; already defining layout_alignParentEnd with targetSdkVersion 34 [RtlHardcoded]
        android:layout_alignParentRight="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_home.xml:63: Warning: Redundant attribute layout_alignParentLeft; already defining layout_alignParentStart with targetSdkVersion 34 [RtlHardcoded]
        android:layout_alignParentLeft="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_home.xml:66: Warning: Redundant attribute layout_alignParentRight; already defining layout_alignParentEnd with targetSdkVersion 34 [RtlHardcoded]
        android:layout_alignParentRight="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_home.xml:87: Warning: Redundant attribute layout_alignParentLeft; already defining layout_alignParentStart with targetSdkVersion 34 [RtlHardcoded]
        android:layout_alignParentLeft="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\fragment_home.xml:90: Warning: Redundant attribute layout_alignParentRight; already defining layout_alignParentEnd with targetSdkVersion 34 [RtlHardcoded]
        android:layout_alignParentRight="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item.xml:23: Warning: Consider replacing android:layout_alignParentRight with android:layout_alignParentEnd="true" to better support right-to-left layouts [RtlHardcoded]
            android:layout_alignParentRight="true"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Vege\MyVG(********)\app\src\main\res\layout\item.xml:25: Warning: Consider replacing android:layout_toRightOf with android:layout_toEndOf="@+id/photo" to better support right-to-left layouts [RtlHardcoded]
            android:layout_toRightOf="@+id/photo"
            ~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RtlHardcoded":
   Using Gravity#LEFT and Gravity#RIGHT can lead to problems when a layout is
   rendered in locales where text flows from right to left. Use Gravity#START
   and Gravity#END instead. Similarly, in XML gravity and layout_gravity
   attributes, use start rather than left.

   For XML attributes such as paddingLeft and layout_marginLeft, use
   paddingStart and layout_marginStart. NOTE: If your minSdkVersion is less
   than 17, you should add both the older left/right attributes as well as the
   new start/end attributes. On older platforms, where RTL is not supported
   and the start/end attributes are unknown and therefore ignored, you need
   the older left/right attributes. There is a separate lint check which
   catches that type of error.

   (Note: For Gravity#LEFT and Gravity#START, you can use these constants even
   when targeting older platforms, because the start bitmask is a superset of
   the left bitmask. Therefore, you can use gravity="start" rather than
   gravity="left|start".)

26 errors, 227 warnings
