<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.1.4" type="conditional_incidents">

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*sourceProvider*0*manifest*0}"
            line="4"
            column="36"
            startOffset="148"
            endLine="4"
            endColumn="77"
            endOffset="189"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="**********"/>
            <entry
                name="read"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/ic_close_black_24dp.xml"
            line="7"
            column="28"
            startOffset="239"
            endLine="7"
            endColumn="46"
            endOffset="257"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/ic_error_outline_black_24dp.xml"
            line="7"
            column="28"
            startOffset="241"
            endLine="7"
            endColumn="45"
            endOffset="258"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/ic_local_bar_black_24dp.xml"
            line="7"
            column="28"
            startOffset="241"
            endLine="7"
            endColumn="46"
            endOffset="259"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_marginLeft`; already defining `layout_marginStart` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_marginLeft"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_marginLeft"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="83"
            column="17"
            startOffset="3037"
            endLine="83"
            endColumn="42"
            endOffset="3062"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;25dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginStart=&quot;25dp&quot;"
            oldString="layout_marginLeft"
            replacement="layout_marginStart"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="119"
            column="17"
            startOffset="4375"
            endLine="119"
            endColumn="42"
            endOffset="4400"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginRight` with `android:layout_marginEnd=&quot;10dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginEnd=&quot;10dp&quot;"
            oldString="layout_marginRight"
            replacement="layout_marginEnd"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="121"
            column="17"
            startOffset="4473"
            endLine="121"
            endColumn="43"
            endOffset="4499"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginRight` with `android:layout_marginEnd=&quot;10dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginEnd=&quot;10dp&quot;"
            oldString="layout_marginRight"
            replacement="layout_marginEnd"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="317"
            column="17"
            startOffset="11944"
            endLine="317"
            endColumn="43"
            endOffset="11970"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="313"
            column="14"
            startOffset="11766"
            endLine="313"
            endColumn="22"
            endOffset="11774"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_marginLeft`; already defining `layout_marginStart` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_marginLeft"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_marginLeft"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc2.xml"
            line="15"
            column="9"
            startOffset="589"
            endLine="15"
            endColumn="34"
            endOffset="614"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_marginRight`; already defining `layout_marginEnd` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_marginRight"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_marginRight"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc2.xml"
            line="16"
            column="9"
            startOffset="631"
            endLine="16"
            endColumn="35"
            endOffset="657"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_marginLeft`; already defining `layout_marginStart` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_marginLeft"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_marginLeft"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc2.xml"
            line="29"
            column="9"
            startOffset="1124"
            endLine="29"
            endColumn="34"
            endOffset="1149"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_marginRight`; already defining `layout_marginEnd` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_marginRight"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_marginRight"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc2.xml"
            line="30"
            column="9"
            startOffset="1166"
            endLine="30"
            endColumn="35"
            endOffset="1192"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_marginRight`; already defining `layout_marginEnd` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_marginRight"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_marginRight"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc2.xml"
            line="47"
            column="9"
            startOffset="1852"
            endLine="47"
            endColumn="35"
            endOffset="1878"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_marginLeft`; already defining `layout_marginStart` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_marginLeft"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_marginLeft"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc2.xml"
            line="81"
            column="9"
            startOffset="3164"
            endLine="81"
            endColumn="34"
            endOffset="3189"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc_content2.xml"
            line="14"
            column="26"
            startOffset="540"
            endLine="14"
            endColumn="30"
            endOffset="544"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_marginLeft`; already defining `layout_marginStart` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_marginLeft"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_marginLeft"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc_content2.xml"
            line="26"
            column="9"
            startOffset="949"
            endLine="26"
            endColumn="34"
            endOffset="974"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_marginLeft`; already defining `layout_marginStart` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_marginLeft"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_marginLeft"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc_content2.xml"
            line="37"
            column="9"
            startOffset="1398"
            endLine="37"
            endColumn="34"
            endOffset="1423"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_marginLeft`; already defining `layout_marginStart` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_marginLeft"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_marginLeft"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc_content2.xml"
            line="48"
            column="9"
            startOffset="1815"
            endLine="48"
            endColumn="34"
            endOffset="1840"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_marginLeft`; already defining `layout_marginStart` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_marginLeft"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_marginLeft"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc_content2.xml"
            line="59"
            column="9"
            startOffset="2266"
            endLine="59"
            endColumn="34"
            endOffset="2291"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_alignParentRight` with `android:layout_alignParentEnd=&quot;true&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_alignParentEnd=&quot;true&quot;"
            oldString="layout_alignParentRight"
            replacement="layout_alignParentEnd"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_negtive.xml"
            line="15"
            column="9"
            startOffset="530"
            endLine="15"
            endColumn="40"
            endOffset="561"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginRight` with `android:layout_marginEnd=&quot;7dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginEnd=&quot;7dp&quot;"
            oldString="layout_marginRight"
            replacement="layout_marginEnd"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_negtive.xml"
            line="17"
            column="9"
            startOffset="618"
            endLine="17"
            endColumn="35"
            endOffset="644"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_alignParentLeft`; already defining `layout_alignParentStart` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_alignParentLeft"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_alignParentLeft"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_negtive.xml"
            line="28"
            column="9"
            startOffset="1061"
            endLine="28"
            endColumn="39"
            endOffset="1091"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_alignParentRight` with `android:layout_alignParentEnd=&quot;true&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_alignParentEnd=&quot;true&quot;"
            oldString="layout_alignParentRight"
            replacement="layout_alignParentEnd"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_positive.xml"
            line="18"
            column="9"
            startOffset="698"
            endLine="18"
            endColumn="40"
            endOffset="729"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_marginRight`; already defining `layout_marginEnd` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_marginRight"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_marginRight"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_positive.xml"
            line="15"
            column="9"
            startOffset="568"
            endLine="15"
            endColumn="35"
            endOffset="594"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_alignParentLeft`; already defining `layout_alignParentStart` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_alignParentLeft"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_alignParentLeft"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_positive.xml"
            line="28"
            column="9"
            startOffset="1106"
            endLine="28"
            endColumn="39"
            endOffset="1136"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_marginLeft`; already defining `layout_marginStart` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_marginLeft"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_marginLeft"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/forum_listitem2.xml"
            line="31"
            column="17"
            startOffset="1093"
            endLine="31"
            endColumn="42"
            endOffset="1118"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_marginLeft`; already defining `layout_marginStart` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_marginLeft"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_marginLeft"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/forum_listitem2.xml"
            line="39"
            column="17"
            startOffset="1437"
            endLine="39"
            endColumn="42"
            endOffset="1462"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_marginLeft`; already defining `layout_marginStart` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_marginLeft"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_marginLeft"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/forum_listitem2.xml"
            line="54"
            column="17"
            startOffset="2041"
            endLine="54"
            endColumn="42"
            endOffset="2066"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_marginRight`; already defining `layout_marginEnd` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_marginRight"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_marginRight"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_favorites.xml"
            line="15"
            column="9"
            startOffset="599"
            endLine="15"
            endColumn="35"
            endOffset="625"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_marginRight`; already defining `layout_marginEnd` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_marginRight"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_marginRight"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_favorites.xml"
            line="71"
            column="9"
            startOffset="2799"
            endLine="71"
            endColumn="35"
            endOffset="2825"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_favorites.xml"
            line="22"
            column="6"
            startOffset="871"
            endLine="22"
            endColumn="14"
            endOffset="879"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_alignParentLeft`; already defining `layout_alignParentStart` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_alignParentLeft"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_alignParentLeft"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="14"
            column="9"
            startOffset="553"
            endLine="14"
            endColumn="39"
            endOffset="583"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_alignParentRight`; already defining `layout_alignParentEnd` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_alignParentRight"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_alignParentRight"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="17"
            column="9"
            startOffset="692"
            endLine="17"
            endColumn="40"
            endOffset="723"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_alignParentLeft`; already defining `layout_alignParentStart` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_alignParentLeft"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_alignParentLeft"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="39"
            column="9"
            startOffset="1591"
            endLine="39"
            endColumn="39"
            endOffset="1621"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_alignParentRight`; already defining `layout_alignParentEnd` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_alignParentRight"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_alignParentRight"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="42"
            column="9"
            startOffset="1730"
            endLine="42"
            endColumn="40"
            endOffset="1761"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_alignParentLeft`; already defining `layout_alignParentStart` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_alignParentLeft"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_alignParentLeft"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="63"
            column="9"
            startOffset="2596"
            endLine="63"
            endColumn="39"
            endOffset="2626"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_alignParentRight`; already defining `layout_alignParentEnd` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_alignParentRight"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_alignParentRight"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="66"
            column="9"
            startOffset="2735"
            endLine="66"
            endColumn="40"
            endOffset="2766"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_alignParentLeft`; already defining `layout_alignParentStart` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_alignParentLeft"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_alignParentLeft"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="87"
            column="9"
            startOffset="3603"
            endLine="87"
            endColumn="39"
            endOffset="3633"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_alignParentRight`; already defining `layout_alignParentEnd` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_alignParentRight"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_alignParentRight"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="90"
            column="9"
            startOffset="3742"
            endLine="90"
            endColumn="40"
            endOffset="3773"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="95"
            column="14"
            startOffset="3498"
            endLine="95"
            endColumn="22"
            endOffset="3506"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="117"
            column="14"
            startOffset="4319"
            endLine="117"
            endColumn="22"
            endOffset="4327"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_alignParentRight` with `android:layout_alignParentEnd=&quot;true&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_alignParentEnd=&quot;true&quot;"
            oldString="layout_alignParentRight"
            replacement="layout_alignParentEnd"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item.xml"
            line="23"
            column="13"
            startOffset="733"
            endLine="23"
            endColumn="44"
            endOffset="764"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_toRightOf` with `android:layout_toEndOf=&quot;@+id/photo&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_toEndOf=&quot;@+id/photo&quot;"
            oldString="layout_toRightOf"
            replacement="layout_toEndOf"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item.xml"
            line="25"
            column="13"
            startOffset="838"
            endLine="25"
            endColumn="37"
            endOffset="862"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

</incidents>
