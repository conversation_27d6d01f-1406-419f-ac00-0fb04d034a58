{"logs": [{"outputFile": "com.example.bottomnavigationview.app-mergeDebugResources-10:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\4973f91cd2444a4abd2089723a7e5dc9\\transformed\\appcompat-v7-28.0.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,329,440,526,628,745,825,903,995,1089,1194,1296,1406,1500,1601,1695,1787,1880,1963,2074,2179,2278,2388,2489,2592,2758,2860", "endColumns": "116,106,110,85,101,116,79,77,91,93,104,101,109,93,100,93,91,92,82,110,104,98,109,100,102,165,101,81", "endOffsets": "217,324,435,521,623,740,820,898,990,1084,1189,1291,1401,1495,1596,1690,1782,1875,1958,2069,2174,2273,2383,2484,2587,2753,2855,2937"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b401c224dd8ca0c8f00c38b06cf5788f\\transformed\\support-compat-28.0.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2942", "endColumns": "100", "endOffsets": "3038"}}]}]}