{"logs": [{"outputFile": "com.example.bottomnavigationview.app-mergeReleaseResources-10:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\4973f91cd2444a4abd2089723a7e5dc9\\transformed\\appcompat-v7-28.0.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,308,420,507,611,729,806,883,974,1067,1163,1257,1358,1451,1546,1640,1731,1822,1905,2009,2114,2214,2324,2431,2539,2701,2799", "endColumns": "102,99,111,86,103,117,76,76,90,92,95,93,100,92,94,93,90,90,82,103,104,99,109,106,107,161,97,82", "endOffsets": "203,303,415,502,606,724,801,878,969,1062,1158,1252,1353,1446,1541,1635,1726,1817,1900,2004,2109,2209,2319,2426,2534,2696,2794,2877"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b401c224dd8ca0c8f00c38b06cf5788f\\transformed\\support-compat-28.0.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2882", "endColumns": "100", "endOffsets": "2978"}}]}]}