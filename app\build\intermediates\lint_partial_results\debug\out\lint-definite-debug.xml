<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.1.4" type="incidents">

    <incident
        id="InefficientWeight"
        severity="warning"
        message="Use a `layout_height` of `0dp` instead of `50dp` for better performance">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="110"
            column="13"
            startOffset="4076"
            endLine="110"
            endColumn="41"
            endOffset="4104"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="607"
                    endOffset="1863"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="834"
                    endOffset="1072"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="1088"
                    endOffset="1327"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="1343"
                    endOffset="1583"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="1599"
                    endOffset="1838"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="22"
            column="14"
            startOffset="835"
            endLine="22"
            endColumn="20"
            endOffset="841"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;首頁&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="27"
            column="17"
            startOffset="1052"
            endLine="27"
            endColumn="34"
            endOffset="1069"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="607"
                    endOffset="1863"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="834"
                    endOffset="1072"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="1088"
                    endOffset="1327"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="1343"
                    endOffset="1583"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="1599"
                    endOffset="1838"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="29"
            column="14"
            startOffset="1089"
            endLine="29"
            endColumn="20"
            endOffset="1095"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;總清單&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="34"
            column="17"
            startOffset="1306"
            endLine="34"
            endColumn="35"
            endOffset="1324"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="607"
                    endOffset="1863"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="834"
                    endOffset="1072"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="1088"
                    endOffset="1327"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="1343"
                    endOffset="1583"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="1599"
                    endOffset="1838"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="36"
            column="14"
            startOffset="1344"
            endLine="36"
            endColumn="20"
            endOffset="1350"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;菜價分析&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="41"
            column="17"
            startOffset="1561"
            endLine="41"
            endColumn="36"
            endOffset="1580"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="607"
                    endOffset="1863"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="834"
                    endOffset="1072"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="1088"
                    endOffset="1327"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="1343"
                    endOffset="1583"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="1599"
                    endOffset="1838"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="43"
            column="14"
            startOffset="1600"
            endLine="43"
            endColumn="20"
            endOffset="1606"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;保存期限&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="48"
            column="17"
            startOffset="1816"
            endLine="48"
            endColumn="36"
            endOffset="1835"/>
    </incident>

    <incident
        id="UselessLeaf"
        severity="warning"
        message="This `LinearLayout` view is unnecessary (no children, no `background`, no `id`, no `style`)">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="60"
            column="14"
            startOffset="2200"
            endLine="60"
            endColumn="26"
            endOffset="2212"/>
    </incident>

    <incident
        id="InefficientWeight"
        severity="warning"
        message="Use a `layout_width` of `0dp` instead of `256dp` for better performance">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="98"
            column="17"
            startOffset="3617"
            endLine="98"
            endColumn="45"
            endOffset="3645"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="78"
            column="14"
            startOffset="2829"
            endLine="78"
            endColumn="23"
            endOffset="2838"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;購買日期:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="93"
            column="17"
            startOffset="3469"
            endLine="93"
            endColumn="37"
            endOffset="3489"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2019-10-08&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="102"
            column="17"
            startOffset="3836"
            endLine="102"
            endColumn="42"
            endOffset="3861"/>
    </incident>

    <incident
        id="NestedWeights"
        severity="warning"
        message="Nested weights are bad for performance">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="135"
            column="17"
            startOffset="4994"
            endLine="135"
            endColumn="42"
            endOffset="5019"/>
    </incident>

    <incident
        id="InefficientWeight"
        severity="warning"
        message="Use a `layout_width` of `0dp` instead of `256dp` for better performance">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="133"
            column="17"
            startOffset="4894"
            endLine="133"
            endColumn="45"
            endOffset="4922"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="115"
            column="14"
            startOffset="4209"
            endLine="115"
            endColumn="23"
            endOffset="4218"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;保存期期:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="128"
            column="17"
            startOffset="4745"
            endLine="128"
            endColumn="37"
            endOffset="4765"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2019-10-15&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="137"
            column="17"
            startOffset="5113"
            endLine="137"
            endColumn="42"
            endOffset="5138"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;種類&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="154"
            column="17"
            startOffset="5702"
            endLine="154"
            endColumn="34"
            endOffset="5719"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;子類&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="185"
            column="17"
            startOffset="6877"
            endLine="185"
            endColumn="34"
            endOffset="6894"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;部位&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="216"
            column="17"
            startOffset="8013"
            endLine="216"
            endColumn="34"
            endOffset="8030"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;份數&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="245"
            column="17"
            startOffset="9139"
            endLine="245"
            endColumn="34"
            endOffset="9156"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;位置&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="276"
            column="17"
            startOffset="10318"
            endLine="276"
            endColumn="34"
            endOffset="10335"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;備註:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="309"
            column="17"
            startOffset="11648"
            endLine="309"
            endColumn="35"
            endOffset="11666"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="313"
            column="14"
            startOffset="11766"
            endLine="313"
            endColumn="22"
            endOffset="11774"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="12243"
                    endOffset="12955"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="12418"
                    endOffset="12665"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="12681"
                    endOffset="12930"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="331"
            column="14"
            startOffset="12419"
            endLine="331"
            endColumn="20"
            endOffset="12425"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;儲存&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="336"
            column="17"
            startOffset="12645"
            endLine="336"
            endColumn="34"
            endOffset="12662"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="12243"
                    endOffset="12955"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="12418"
                    endOffset="12665"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
                    startOffset="12681"
                    endOffset="12930"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="338"
            column="14"
            startOffset="12682"
            endLine="338"
            endColumn="20"
            endOffset="12688"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;再記一筆&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="343"
            column="17"
            startOffset="12908"
            endLine="343"
            endColumn="36"
            endOffset="12927"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Button&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml"
            line="351"
            column="13"
            startOffset="13163"
            endLine="351"
            endColumn="34"
            endOffset="13184"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `RelativeLayout` parent is unnecessary">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buymoney.xml"
            line="9"
            column="6"
            startOffset="352"
            endLine="9"
            endColumn="18"
            endOffset="364"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;歷史菜價&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buymoney.xml"
            line="20"
            column="13"
            startOffset="752"
            endLine="20"
            endColumn="32"
            endOffset="771"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;價格高至低&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buymoney.xml"
            line="33"
            column="17"
            startOffset="1216"
            endLine="33"
            endColumn="37"
            endOffset="1236"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;價格低至高&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buymoney.xml"
            line="40"
            column="17"
            startOffset="1473"
            endLine="40"
            endColumn="37"
            endOffset="1493"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_buymoney.xml"
            line="42"
            column="14"
            startOffset="1513"
            endLine="42"
            endColumn="23"
            endOffset="1522"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `RelativeLayout` parent is unnecessary">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_pre_vegetables.xml"
            line="9"
            column="6"
            startOffset="352"
            endLine="9"
            endColumn="18"
            endOffset="364"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;總清單&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_pre_vegetables.xml"
            line="20"
            column="13"
            startOffset="752"
            endLine="20"
            endColumn="31"
            endOffset="770"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `RelativeLayout` parent is unnecessary">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_vganalysis.xml"
            line="9"
            column="6"
            startOffset="352"
            endLine="9"
            endColumn="18"
            endOffset="364"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;保存提醒&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_vganalysis.xml"
            line="20"
            column="13"
            startOffset="752"
            endLine="20"
            endColumn="32"
            endOffset="771"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;快到期&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_vganalysis.xml"
            line="33"
            column="17"
            startOffset="1216"
            endLine="33"
            endColumn="35"
            endOffset="1234"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;已到期&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_vganalysis.xml"
            line="40"
            column="17"
            startOffset="1471"
            endLine="40"
            endColumn="35"
            endOffset="1489"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_vganalysis.xml"
            line="42"
            column="14"
            startOffset="1509"
            endLine="42"
            endColumn="25"
            endOffset="1520"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_welcome.xml"
            line="9"
            column="6"
            startOffset="382"
            endLine="9"
            endColumn="15"
            endOffset="391"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TextView&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc2.xml"
            line="19"
            column="9"
            startOffset="757"
            endLine="19"
            endColumn="32"
            endOffset="780"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc2.xml"
            line="24"
            column="6"
            startOffset="949"
            endLine="24"
            endColumn="14"
            endOffset="957"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;請輸入留言&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc2.xml"
            line="34"
            column="9"
            startOffset="1315"
            endLine="34"
            endColumn="29"
            endOffset="1335"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;送出&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc2.xml"
            line="49"
            column="9"
            startOffset="1933"
            endLine="49"
            endColumn="26"
            endOffset="1950"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TextView&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc2.xml"
            line="73"
            column="9"
            startOffset="2862"
            endLine="73"
            endColumn="32"
            endOffset="2885"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;說:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc2.xml"
            line="83"
            column="9"
            startOffset="3247"
            endLine="83"
            endColumn="26"
            endOffset="3264"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TextView&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc_content2.xml"
            line="17"
            column="9"
            startOffset="612"
            endLine="17"
            endColumn="32"
            endOffset="635"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TextView&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc_content2.xml"
            line="28"
            column="9"
            startOffset="1032"
            endLine="28"
            endColumn="32"
            endOffset="1055"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;留言者&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc_content2.xml"
            line="40"
            column="9"
            startOffset="1521"
            endLine="40"
            endColumn="27"
            endOffset="1539"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2018/01/01&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc_content2.xml"
            line="50"
            column="9"
            startOffset="1898"
            endLine="50"
            endColumn="34"
            endOffset="1923"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;留言時間:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc_content2.xml"
            line="61"
            column="9"
            startOffset="2349"
            endLine="61"
            endColumn="29"
            endOffset="2369"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Empty `contentDescription` attribute on image">
        <fix-attribute
            description="Set contentDescription"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="contentDescription"
            value="TODO"
            dot="4"
            mark="0"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_negtive.xml"
            line="18"
            column="9"
            startOffset="660"
            endLine="18"
            endColumn="42"
            endOffset="693"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TODO&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_negtive.xml"
            line="18"
            column="9"
            startOffset="660"
            endLine="18"
            endColumn="42"
            endOffset="693"/>
    </incident>

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the layout file, `androidx.cardview.widget.CardView`, was not found in the project or the libraries">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_negtive.xml"
            line="22"
            column="5"
            startOffset="792"
            endLine="107"
            endColumn="41"
            endOffset="3845"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_negtive.xml"
            line="36"
            column="14"
            startOffset="1333"
            endLine="36"
            endColumn="26"
            endOffset="1345"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Empty `contentDescription` attribute on image">
        <fix-attribute
            description="Set contentDescription"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="contentDescription"
            value="TODO"
            dot="4"
            mark="0"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_negtive.xml"
            line="62"
            column="21"
            startOffset="2587"
            endLine="62"
            endColumn="54"
            endOffset="2620"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TODO&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_negtive.xml"
            line="62"
            column="21"
            startOffset="2587"
            endLine="62"
            endColumn="54"
            endOffset="2620"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Try Again&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_negtive.xml"
            line="83"
            column="21"
            startOffset="3494"
            endLine="83"
            endColumn="45"
            endOffset="3518"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Empty `contentDescription` attribute on image">
        <fix-attribute
            description="Set contentDescription"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="contentDescription"
            value="TODO"
            dot="4"
            mark="0"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_positive.xml"
            line="19"
            column="9"
            startOffset="746"
            endLine="19"
            endColumn="42"
            endOffset="779"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TODO&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_positive.xml"
            line="19"
            column="9"
            startOffset="746"
            endLine="19"
            endColumn="42"
            endOffset="779"/>
    </incident>

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the layout file, `androidx.cardview.widget.CardView`, was not found in the project or the libraries">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_positive.xml"
            line="22"
            column="5"
            startOffset="830"
            endLine="113"
            endColumn="41"
            endOffset="4220"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_positive.xml"
            line="36"
            column="14"
            startOffset="1378"
            endLine="36"
            endColumn="26"
            endOffset="1390"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_positive.xml"
            line="50"
            column="21"
            startOffset="2021"
            endLine="50"
            endColumn="44"
            endOffset="2044"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_positive.xml"
            line="54"
            column="18"
            startOffset="2186"
            endLine="54"
            endColumn="27"
            endOffset="2195"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_positive.xml"
            line="70"
            column="21"
            startOffset="2991"
            endLine="70"
            endColumn="44"
            endOffset="3014"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;+57 points&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_positive.xml"
            line="82"
            column="21"
            startOffset="3572"
            endLine="82"
            endColumn="46"
            endOffset="3597"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_positive.xml"
            line="80"
            column="21"
            startOffset="3481"
            endLine="80"
            endColumn="44"
            endOffset="3504"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;YESSSSSS&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_positive.xml"
            line="89"
            column="21"
            startOffset="3868"
            endLine="89"
            endColumn="44"
            endOffset="3891"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Refresh&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fireforulist2.xml"
            line="16"
            column="9"
            startOffset="649"
            endLine="16"
            endColumn="31"
            endOffset="671"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;食譜專區&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fireforulist2.xml"
            line="43"
            column="9"
            startOffset="1728"
            endLine="43"
            endColumn="28"
            endOffset="1747"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary; transfer the `background` attribute to the other view">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/forum_listitem2.xml"
            line="10"
            column="6"
            startOffset="334"
            endLine="10"
            endColumn="18"
            endOffset="346"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/forum_listitem2.xml"
            line="15"
            column="10"
            startOffset="493"
            endLine="15"
            endColumn="19"
            endOffset="502"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2018/01/01&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/forum_listitem2.xml"
            line="32"
            column="17"
            startOffset="1142"
            endLine="32"
            endColumn="42"
            endOffset="1167"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TextView&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/forum_listitem2.xml"
            line="40"
            column="17"
            startOffset="1486"
            endLine="40"
            endColumn="40"
            endOffset="1509"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;最後更新:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/forum_listitem2.xml"
            line="47"
            column="17"
            startOffset="1764"
            endLine="47"
            endColumn="37"
            endOffset="1784"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;推薦給&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/forum_listitem2.xml"
            line="55"
            column="17"
            startOffset="2090"
            endLine="55"
            endColumn="35"
            endOffset="2108"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Subject&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/forum_listitem2.xml"
            line="62"
            column="13"
            startOffset="2318"
            endLine="62"
            endColumn="35"
            endOffset="2340"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/forum_listitem2.xml"
            line="63"
            column="13"
            startOffset="2354"
            endLine="63"
            endColumn="36"
            endOffset="2377"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;請輸入暱稱&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_favorites.xml"
            line="17"
            column="9"
            startOffset="682"
            endLine="17"
            endColumn="29"
            endOffset="702"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_favorites.xml"
            line="22"
            column="6"
            startOffset="871"
            endLine="22"
            endColumn="14"
            endOffset="879"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_favorites.xml"
            line="38"
            column="6"
            startOffset="1499"
            endLine="38"
            endColumn="15"
            endOffset="1508"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;準備中&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_favorites.xml"
            line="58"
            column="9"
            startOffset="2289"
            endLine="58"
            endColumn="27"
            endOffset="2307"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;食譜推薦區&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_favorites.xml"
            line="73"
            column="9"
            startOffset="2879"
            endLine="73"
            endColumn="29"
            endOffset="2899"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;買菜&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="25"
            column="9"
            startOffset="1021"
            endLine="25"
            endColumn="26"
            endOffset="1038"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;總清單&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="49"
            column="9"
            startOffset="2026"
            endLine="49"
            endColumn="27"
            endOffset="2044"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;到期食材&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="73"
            column="9"
            startOffset="3031"
            endLine="73"
            endColumn="28"
            endOffset="3050"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;菜價分析&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="97"
            column="9"
            startOffset="4038"
            endLine="97"
            endColumn="28"
            endOffset="4057"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `RelativeLayout` parent is unnecessary; transfer the `background` attribute to the other view">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="8"
            column="6"
            startOffset="322"
            endLine="8"
            endColumn="18"
            endOffset="334"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;個人選項&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="19"
            column="13"
            startOffset="718"
            endLine="19"
            endColumn="32"
            endOffset="737"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="27"
            column="14"
            startOffset="965"
            endLine="27"
            endColumn="23"
            endOffset="974"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;     個人化食譜選項&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="40"
            column="17"
            startOffset="1516"
            endLine="40"
            endColumn="44"
            endOffset="1543"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="49"
            column="14"
            startOffset="1800"
            endLine="49"
            endColumn="23"
            endOffset="1809"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;     個人資料&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="62"
            column="17"
            startOffset="2349"
            endLine="62"
            endColumn="41"
            endOffset="2373"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="71"
            column="14"
            startOffset="2630"
            endLine="71"
            endColumn="23"
            endOffset="2639"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;     關於我們&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="84"
            column="17"
            startOffset="3177"
            endLine="84"
            endColumn="41"
            endOffset="3201"/>
    </incident>

    <incident
        id="InefficientWeight"
        severity="warning"
        message="Use a `layout_width` of `0dp` instead of `wrap_content` for better performance">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="97"
            column="17"
            startOffset="3568"
            endLine="97"
            endColumn="52"
            endOffset="3603"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="95"
            column="14"
            startOffset="3498"
            endLine="95"
            endColumn="22"
            endOffset="3506"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;請輸入信箱&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="102"
            column="17"
            startOffset="3804"
            endLine="102"
            endColumn="37"
            endOffset="3824"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="104"
            column="14"
            startOffset="3844"
            endLine="104"
            endColumn="23"
            endOffset="3853"/>
    </incident>

    <incident
        id="InefficientWeight"
        severity="warning"
        message="Use a `layout_width` of `0dp` instead of `wrap_content` for better performance">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="119"
            column="17"
            startOffset="4390"
            endLine="119"
            endColumn="52"
            endOffset="4425"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="117"
            column="14"
            startOffset="4319"
            endLine="117"
            endColumn="22"
            endOffset="4327"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;請輸入電話&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="124"
            column="17"
            startOffset="4626"
            endLine="124"
            endColumn="37"
            endOffset="4646"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="126"
            column="14"
            startOffset="4666"
            endLine="126"
            endColumn="23"
            endOffset="4675"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;請輸入您的寶貴意見&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="140"
            column="17"
            startOffset="5258"
            endLine="140"
            endColumn="41"
            endOffset="5282"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;送出&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="149"
            column="17"
            startOffset="5650"
            endLine="149"
            endColumn="34"
            endOffset="5667"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item.xml"
            line="11"
            column="10"
            startOffset="257"
            endLine="11"
            endColumn="19"
            endOffset="266"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item.xml"
            line="28"
            column="13"
            startOffset="969"
            endLine="28"
            endColumn="37"
            endOffset="993"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TextView&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item1.xml"
            line="19"
            column="13"
            startOffset="717"
            endLine="19"
            endColumn="36"
            endOffset="740"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item1.xml"
            line="25"
            column="6"
            startOffset="933"
            endLine="25"
            endColumn="15"
            endOffset="942"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;種類：&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item1.xml"
            line="52"
            column="17"
            startOffset="1918"
            endLine="52"
            endColumn="35"
            endOffset="1936"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TextView&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item1.xml"
            line="58"
            column="17"
            startOffset="2129"
            endLine="58"
            endColumn="40"
            endOffset="2152"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;子類：&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item1.xml"
            line="71"
            column="17"
            startOffset="2581"
            endLine="71"
            endColumn="35"
            endOffset="2599"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TextView&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item1.xml"
            line="77"
            column="17"
            startOffset="2796"
            endLine="77"
            endColumn="40"
            endOffset="2819"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;部位：&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item1.xml"
            line="91"
            column="17"
            startOffset="3310"
            endLine="91"
            endColumn="35"
            endOffset="3328"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TextView&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item1.xml"
            line="97"
            column="17"
            startOffset="3521"
            endLine="97"
            endColumn="40"
            endOffset="3544"/>
    </incident>

    <incident
        id="NestedWeights"
        severity="warning"
        message="Nested weights are bad for performance">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item1.xml"
            line="134"
            column="13"
            startOffset="4688"
            endLine="134"
            endColumn="38"
            endOffset="4713"/>
    </incident>

    <incident
        id="InefficientWeight"
        severity="warning"
        message="Use a `layout_height` of `0dp` instead of `match_parent` for better performance">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item1.xml"
            line="133"
            column="13"
            startOffset="4638"
            endLine="133"
            endColumn="49"
            endOffset="4674"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;購買日期：&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item1.xml"
            line="118"
            column="17"
            startOffset="4177"
            endLine="118"
            endColumn="37"
            endOffset="4197"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TextView&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item1.xml"
            line="126"
            column="13"
            startOffset="4400"
            endLine="126"
            endColumn="36"
            endOffset="4423"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item2money.xml"
            line="2"
            column="2"
            startOffset="41"
            endLine="2"
            endColumn="14"
            endOffset="53"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TextView&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item2money.xml"
            line="19"
            column="13"
            startOffset="694"
            endLine="19"
            endColumn="36"
            endOffset="717"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;種類：&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item2money.xml"
            line="43"
            column="17"
            startOffset="1571"
            endLine="43"
            endColumn="35"
            endOffset="1589"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TextView&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item2money.xml"
            line="49"
            column="17"
            startOffset="1782"
            endLine="49"
            endColumn="40"
            endOffset="1805"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;數量：&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item2money.xml"
            line="62"
            column="17"
            startOffset="2234"
            endLine="62"
            endColumn="35"
            endOffset="2252"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TextView&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item2money.xml"
            line="68"
            column="17"
            startOffset="2449"
            endLine="68"
            endColumn="40"
            endOffset="2472"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;購買地點：&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item2money.xml"
            line="82"
            column="17"
            startOffset="2961"
            endLine="82"
            endColumn="37"
            endOffset="2981"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TextView&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item2money.xml"
            line="88"
            column="17"
            startOffset="3174"
            endLine="88"
            endColumn="40"
            endOffset="3197"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;購買日期：&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item2money.xml"
            line="101"
            column="17"
            startOffset="3630"
            endLine="101"
            endColumn="37"
            endOffset="3650"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TextView&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item2money.xml"
            line="107"
            column="17"
            startOffset="3846"
            endLine="107"
            endColumn="40"
            endOffset="3869"/>
    </incident>

    <incident
        id="NestedWeights"
        severity="warning"
        message="Nested weights are bad for performance">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item2money.xml"
            line="126"
            column="13"
            startOffset="4377"
            endLine="126"
            endColumn="40"
            endOffset="4404"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;元&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item2money.xml"
            line="137"
            column="13"
            startOffset="4780"
            endLine="137"
            endColumn="29"
            endOffset="4796"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;主頁&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/menu/bottom_navigation.xml"
            line="6"
            column="9"
            startOffset="215"
            endLine="6"
            endColumn="27"
            endOffset="233"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;推薦食譜&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/menu/bottom_navigation.xml"
            line="10"
            column="9"
            startOffset="366"
            endLine="10"
            endColumn="29"
            endOffset="386"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;個人&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/menu/bottom_navigation.xml"
            line="14"
            column="9"
            startOffset="502"
            endLine="14"
            endColumn="27"
            endOffset="520"/>
    </incident>

    <incident
        id="MonochromeLauncherIcon"
        severity="warning"
        message="The application adaptive roundIcon is missing a monochrome tag">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="17"
            endOffset="276"/>
    </incident>

    <incident
        id="Typos"
        severity="warning"
        message="&quot;Wanna&quot; is a common misspelling; did you mean &quot;Want to&quot;?">
        <fix-alternatives>
            <fix-replace
                description="Replace with &quot;Want to&quot;"
                oldString="Wanna"
                replacement="Want to"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="50"
            column="102"
            startOffset="1414"
            endLine="50"
            endColumn="102"
            endOffset="1419"/>
    </incident>

    <incident
        id="SourceLockedOrientationActivity"
        severity="warning"
        message="You should not lock orientation of your activities, so that you can support a good user experience for any device or orientation">
        <fix-replace
            description="Set the orientation to SCREEN_ORIENTATION_UNSPECIFIED"
            oldString="SCREEN_ORIENTATION_PORTRAIT"
            replacement="SCREEN_ORIENTATION_UNSPECIFIED"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/BuyActivity.java"
            line="101"
            column="9"
            startOffset="3383"
            endLine="101"
            endColumn="74"
            endOffset="3448"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/BuyActivity.java"
            line="128"
            column="25"
            startOffset="4552"
            endLine="128"
            endColumn="118"
            endOffset="4645"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Number formatting does not take into account locale settings. Consider using `String.format` instead.">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/BuyActivity.java"
            line="128"
            column="25"
            startOffset="4552"
            endLine="128"
            endColumn="51"
            endOffset="4578"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Number formatting does not take into account locale settings. Consider using `String.format` instead.">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/BuyActivity.java"
            line="128"
            column="58"
            startOffset="4585"
            endLine="128"
            endColumn="85"
            endOffset="4612"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Number formatting does not take into account locale settings. Consider using `String.format` instead.">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/BuyActivity.java"
            line="128"
            column="92"
            startOffset="4619"
            endLine="128"
            endColumn="118"
            endOffset="4645"/>
    </incident>

    <incident
        id="ShowToast"
        severity="warning"
        message="Toast created but not shown: did you forget to call `show()`?">
        <fix-replace
            description="Call show()"
            oldString="_lint_insert_end_"
            replacement=".show()">
            <range
                file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/BuyActivity.java"
                startOffset="18282"
                endOffset="18331"/>
        </fix-replace>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/BuyActivity.java"
            line="514"
            column="13"
            startOffset="18282"
            endLine="514"
            endColumn="27"
            endOffset="18296"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/BuyActivity.java"
            line="557"
            column="31"
            startOffset="19307"
            endLine="557"
            endColumn="74"
            endOffset="19350"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/BuyActivity.java"
            line="557"
            column="45"
            startOffset="19321"
            endLine="557"
            endColumn="54"
            endOffset="19330"/>
    </incident>

    <incident
        id="Recycle"
        severity="warning"
        message="This `Cursor` should be freed up after use with `#close()`">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/BuyMoney.java"
            line="91"
            column="36"
            startOffset="3040"
            endLine="91"
            endColumn="44"
            endOffset="3048"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/BuyMoney.java"
            line="115"
            column="50"
            startOffset="3920"
            endLine="115"
            endColumn="79"
            endOffset="3949"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/BuyMoney.java"
            line="118"
            column="52"
            startOffset="4071"
            endLine="118"
            endColumn="83"
            endOffset="4102"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/BuyMoney.java"
            line="120"
            column="53"
            startOffset="4158"
            endLine="120"
            endColumn="85"
            endOffset="4190"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/BuyMoney.java"
            line="122"
            column="54"
            startOffset="4247"
            endLine="122"
            endColumn="87"
            endOffset="4280"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/BuyMoney.java"
            line="124"
            column="48"
            startOffset="4331"
            endLine="124"
            endColumn="76"
            endOffset="4359"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/BuyMoney.java"
            line="126"
            column="52"
            startOffset="4414"
            endLine="126"
            endColumn="85"
            endOffset="4447"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"/>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/FavoritesFragment.java"
            line="104"
            column="73"
            startOffset="4055"
            endLine="104"
            endColumn="81"
            endOffset="4063"/>
    </incident>

    <incident
        id="ViewHolder"
        severity="warning"
        message="Unconditional layout inflation from view adapter: Should use View Holder pattern (use recycled view passed into this method as the second parameter) for smoother scrolling">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/FireDiscActivity2.java"
            line="131"
            column="27"
            startOffset="4598"
            endLine="131"
            endColumn="73"
            endOffset="4644"/>
    </incident>

    <incident
        id="SimpleDateFormat"
        severity="warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/FireDiscActivity2.java"
            line="135"
            column="49"
            startOffset="4898"
            endLine="135"
            endColumn="84"
            endOffset="4933"/>
    </incident>

    <incident
        id="HandlerLeak"
        severity="warning"
        message="This `Handler` class should be static or leaks might occur (anonymous android.os.Handler)">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/FireForumListActivity2.java"
            line="72"
            column="31"
            startOffset="2266"
            endLine="82"
            endColumn="6"
            endOffset="2937"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/FireForumListActivity2.java"
            line="134"
            column="41"
            startOffset="5410"
            endLine="134"
            endColumn="71"
            endOffset="5440"/>
    </incident>

    <incident
        id="SimpleDateFormat"
        severity="warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/FireForumListActivity2.java"
            line="272"
            column="49"
            startOffset="10568"
            endLine="272"
            endColumn="84"
            endOffset="10603"/>
    </incident>

    <incident
        id="ShowToast"
        severity="warning"
        message="Toast created but not shown: did you forget to call `show()`?">
        <fix-replace
            description="Call show()"
            oldString="_lint_insert_end_"
            replacement=".show()">
            <range
                file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/FragmentList.java"
                startOffset="1145"
                endOffset="1208"/>
        </fix-replace>
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/FragmentList.java"
            line="37"
            column="9"
            startOffset="1145"
            endLine="37"
            endColumn="23"
            endOffset="1159"/>
    </incident>

    <incident
        id="Recycle"
        severity="warning"
        message="This `Cursor` should be freed up after use with `#close()`">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/PreVegetables.java"
            line="95"
            column="36"
            startOffset="3488"
            endLine="95"
            endColumn="44"
            endOffset="3496"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/PreVegetables.java"
            line="119"
            column="52"
            startOffset="4330"
            endLine="119"
            endColumn="81"
            endOffset="4359"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/PreVegetables.java"
            line="122"
            column="56"
            startOffset="4487"
            endLine="122"
            endColumn="89"
            endOffset="4520"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/PreVegetables.java"
            line="123"
            column="53"
            startOffset="4575"
            endLine="123"
            endColumn="85"
            endOffset="4607"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/PreVegetables.java"
            line="124"
            column="54"
            startOffset="4663"
            endLine="124"
            endColumn="87"
            endOffset="4696"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/PreVegetables.java"
            line="125"
            column="48"
            startOffset="4746"
            endLine="125"
            endColumn="76"
            endOffset="4774"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/PreVegetables.java"
            line="126"
            column="51"
            startOffset="4827"
            endLine="126"
            endColumn="80"
            endOffset="4856"/>
    </incident>

    <incident
        id="Recycle"
        severity="warning"
        message="This `Cursor` should be freed up after use with `#close()`">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/VGanalysis.java"
            line="89"
            column="28"
            startOffset="2869"
            endLine="89"
            endColumn="36"
            endOffset="2877"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/VGanalysis.java"
            line="118"
            column="44"
            startOffset="3991"
            endLine="118"
            endColumn="73"
            endOffset="4020"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/VGanalysis.java"
            line="121"
            column="48"
            startOffset="4132"
            endLine="121"
            endColumn="81"
            endOffset="4165"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/VGanalysis.java"
            line="122"
            column="45"
            startOffset="4212"
            endLine="122"
            endColumn="77"
            endOffset="4244"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/VGanalysis.java"
            line="123"
            column="46"
            startOffset="4292"
            endLine="123"
            endColumn="79"
            endOffset="4325"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/VGanalysis.java"
            line="124"
            column="40"
            startOffset="4367"
            endLine="124"
            endColumn="68"
            endOffset="4395"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/VGanalysis.java"
            line="125"
            column="43"
            startOffset="4440"
            endLine="125"
            endColumn="72"
            endOffset="4469"/>
    </incident>

    <incident
        id="HandlerLeak"
        severity="warning"
        message="This `Handler` class should be static or leaks might occur (anonymous android.os.Handler)">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/WelcomeActivty.java"
            line="25"
            column="32"
            startOffset="700"
            endLine="42"
            endColumn="6"
            endOffset="1210"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 36"
            oldString="34"
            replacement="36"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="13"
            column="9"
            startOffset="306"
            endLine="13"
            endColumn="21"
            endOffset="318"/>
    </incident>

    <incident
        id="GradleCompatible"
        severity="fatal"
        message="Version 28 (intended for Android Pie and below) is the last version of the legacy support library, so we recommend that you migrate to AndroidX libraries when using Android Q and moving forward. The IDE can help with this: Refactor > Migrate to AndroidX...">
        <location
            file="${:app*projectDir}/build.gradle"
            line="46"
            column="20"
            startOffset="1115"
            endLine="46"
            endColumn="61"
            endOffset="1156"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.android.support.constraint:constraint-layout than 1.1.3 is available: 2.0.4">
        <fix-replace
            description="Change to 2.0.4"
            family="Update versions"
            oldString="1.1.3"
            replacement="2.0.4"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="47"
            column="20"
            startOffset="1177"
            endLine="47"
            endColumn="76"
            endOffset="1233"/>
    </incident>

    <incident
        id="GradleCompatible"
        severity="fatal"
        message="Version 28 (intended for Android Pie and below) is the last version of the legacy support library, so we recommend that you migrate to AndroidX libraries when using Android Q and moving forward. The IDE can help with this: Refactor > Migrate to AndroidX...">
        <location
            file="${:app*projectDir}/build.gradle"
            line="49"
            column="20"
            startOffset="1311"
            endLine="49"
            endColumn="60"
            endOffset="1351"/>
    </incident>

    <incident
        id="GradleCompatible"
        severity="fatal"
        message="Version 28 (intended for Android Pie and below) is the last version of the legacy support library, so we recommend that you migrate to AndroidX libraries when using Android Q and moving forward. The IDE can help with this: Refactor > Migrate to AndroidX...">
        <location
            file="${:app*projectDir}/build.gradle"
            line="50"
            column="20"
            startOffset="1372"
            endLine="50"
            endColumn="55"
            endOffset="1407"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/bill.jpg` in densityless folder">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/bill.jpg"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/cookbear.jpg` in densityless folder">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/cookbear.jpg"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/cookbear2.jpg` in densityless folder">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/cookbear2.jpg"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/meat.jpg` in densityless folder">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/meat.jpg"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/seafood.jpg` in densityless folder">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/seafood.jpg"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/speech_bubble.png` in densityless folder">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/speech_bubble.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/takepic.png` in densityless folder">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/takepic.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/vege1.jpg` in densityless folder">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/vege1.jpg"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/vegetable.jpg` in densityless folder">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/vegetable.jpg"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/welcom.jpg` in densityless folder">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/welcom.jpg"/>
    </incident>

    <incident
        id="OnClick"
        severity="error"
        message="Corresponding method handler &apos;`public void BtBuy(android.view.View)`&apos; not found">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="24"
            column="9"
            startOffset="988"
            endLine="24"
            endColumn="32"
            endOffset="1011"/>
    </incident>

    <incident
        id="OnClick"
        severity="error"
        message="Corresponding method handler &apos;`public void enterForum(android.view.View)`&apos; not found">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_favorites.xml"
            line="72"
            column="9"
            startOffset="2841"
            endLine="72"
            endColumn="37"
            endOffset="2869"/>
    </incident>

    <incident
        id="InflateParams"
        severity="warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/FireDiscActivity2.java"
            line="131"
            column="68"
            startOffset="4639"
            endLine="131"
            endColumn="72"
            endOffset="4643"/>
    </incident>

    <incident
        id="InflateParams"
        severity="warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/bottomnavigationview/FireForumListActivity2.java"
            line="258"
            column="89"
            startOffset="9788"
            endLine="258"
            endColumn="93"
            endOffset="9792"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@android:color/holo_red_dark` with a theme that also paints a background (inferred theme is `@style/AppTheme`)">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="7"
            column="5"
            startOffset="312"
            endLine="7"
            endColumn="54"
            endOffset="361"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#ff85ff` with a theme that also paints a background (inferred theme is `@style/AppTheme`)">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc2.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="33"
            endOffset="369"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#ffffff` with a theme that also paints a background (inferred theme is `@style/AppTheme`)">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/disc_content2.xml"
            line="6"
            column="5"
            startOffset="263"
            endLine="6"
            endColumn="33"
            endOffset="291"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#ffd47a` with a theme that also paints a background (inferred theme is `@style/AppTheme`)">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fireforulist2.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="33"
            endOffset="369"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#ffffff` with a theme that also paints a background (inferred theme is `@style/AppTheme`)">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/forum_listitem2.xml"
            line="7"
            column="5"
            startOffset="260"
            endLine="7"
            endColumn="33"
            endOffset="288"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#fffc7a` with a theme that also paints a background (inferred theme is `@style/AppTheme`)">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_favorites.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="33"
            endOffset="369"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#009688` with a theme that also paints a background (inferred theme is `@style/AppTheme`)">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="33"
            endOffset="369"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@android:color/holo_green_light` with a theme that also paints a background (inferred theme is `@style/AppTheme`)">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml"
            line="6"
            column="5"
            startOffset="260"
            endLine="6"
            endColumn="57"
            endOffset="312"/>
    </incident>

</incidents>
