<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/holo_green_light">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/textView20"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="個人選項"
            android:textSize="36sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/imageView4"
                android:layout_width="20dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.1"
                app:srcCompat="@mipmap/ic_launcher_round" />

            <TextView
                android:id="@+id/textView21"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="     個人化食譜選項"
                android:textSize="18sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/imageVie4"
                android:layout_width="20dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.1"
                app:srcCompat="@mipmap/ic_launcher_round" />

            <TextView
                android:id="@+id/textViw21"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="     個人資料"
                android:textSize="18sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/imageie4"
                android:layout_width="20dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.1"
                app:srcCompat="@mipmap/ic_launcher_round" />

            <TextView
                android:id="@+id/texViw21"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="     關於我們"
                android:textSize="18sp" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/editText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:ems="10"
                android:inputType="textPersonName"
                android:text="請輸入信箱" />

            <ImageView
                android:id="@+id/imageView"
                android:layout_width="40dp"
                android:layout_height="wrap_content"
                app:srcCompat="@android:drawable/sym_action_email" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/editText1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:ems="10"
                android:inputType="textPersonName"
                android:text="請輸入電話" />

            <ImageView
                android:id="@+id/imageView1"
                android:layout_width="40dp"
                android:layout_height="wrap_content"
                app:srcCompat="@android:drawable/stat_sys_phone_call" />
        </LinearLayout>

        <android.support.design.widget.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <android.support.design.widget.TextInputEditText
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:hint="請輸入您的寶貴意見" />

            <Button
                android:id="@+id/button"
                style="@style/Widget.AppCompat.Button.ButtonBar.AlertDialog"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:background="@color/colorPrimaryDark"
                android:text="送出"
                android:textColor="@color/colorAccent"
                android:textSize="18sp" />
        </android.support.design.widget.TextInputLayout>

    </LinearLayout>
</RelativeLayout>