{"logs": [{"outputFile": "com.example.bottomnavigationview.app-mergeReleaseResources-10:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b401c224dd8ca0c8f00c38b06cf5788f\\transformed\\support-compat-28.0.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2903", "endColumns": "100", "endOffsets": "2999"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\4973f91cd2444a4abd2089723a7e5dc9\\transformed\\appcompat-v7-28.0.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,322,429,517,622,738,827,914,1005,1098,1193,1287,1388,1481,1576,1670,1761,1852,1936,2045,2150,2248,2358,2457,2563,2722,2821", "endColumns": "109,106,106,87,104,115,88,86,90,92,94,93,100,92,94,93,90,90,83,108,104,97,109,98,105,158,98,81", "endOffsets": "210,317,424,512,617,733,822,909,1000,1093,1188,1282,1383,1476,1571,1665,1756,1847,1931,2040,2145,2243,2353,2452,2558,2717,2816,2898"}}]}]}