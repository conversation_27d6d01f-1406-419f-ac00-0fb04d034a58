-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:2:1-39:12
INJECTED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:2:1-39:12
INJECTED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:2:1-39:12
INJECTED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:2:1-39:12
MERGED from [com.android.support:design:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8270adb3dcd9272142798ba9958ab579\transformed\design-28.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.android.support:appcompat-v7:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4973f91cd2444a4abd2089723a7e5dc9\transformed\appcompat-v7-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support.constraint:constraint-layout:1.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\868eef2cd99d9b153fbf31a49138b958\transformed\constraint-layout-1.1.3\AndroidManifest.xml:2:1-11:12
MERGED from [com.android.support:multidex:1.0.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\bbd9ec28dd32ea78e0900a1ea7d9bc6c\transformed\multidex-1.0.3\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:cardview-v7:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a7298b63126ca22b56a14d4e307079ac\transformed\cardview-v7-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-fragment:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebd18f75b9a3b0d73784dd6a5cbc30ef\transformed\support-fragment-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:animated-vector-drawable:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3fae80cda322ff85344e8c99f2ac9c5\transformed\animated-vector-drawable-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:recyclerview-v7:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\75fa7e361cf1fa871ae34561b272908c\transformed\recyclerview-v7-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-core-ui:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\820ed1c3d2f0c9380d113a103fb22ade\transformed\support-core-ui-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-core-utils:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bac4bf448ecaf23f82c8969bbd354931\transformed\support-core-utils-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-vector-drawable:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a8fb577233d540612a6dcc52cc13314\transformed\support-vector-drawable-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:transition:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa87da10e9530b3689a72358af980e44\transformed\transition-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:loader:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8c24645226d7261301ca11338088ba59\transformed\loader-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:viewpager:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7caf09b9e32c19af8bc0db15aa107d83\transformed\viewpager-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:coordinatorlayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f29d80ca58f1f9b594bfd54574416d70\transformed\coordinatorlayout-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:drawerlayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a0002004a593a9099acf9a4068e806c\transformed\drawerlayout-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:slidingpanelayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\12278b8824a007d7c0d94cd6b99512f2\transformed\slidingpanelayout-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:customview:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3b419f68d713d52fa704e9f28db1c3dd\transformed\customview-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:swiperefreshlayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62212203febe87e0c44738abb5569c8e\transformed\swiperefreshlayout-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:asynclayoutinflater:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a18a0e7a8c82281bb74b0acfa9b21599\transformed\asynclayoutinflater-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-compat:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b401c224dd8ca0c8f00c38b06cf5788f\transformed\support-compat-28.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.android.support:versionedparcelable:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe7de84392061693ae5f87ceab83d73e\transformed\versionedparcelable-28.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.android.support:cursoradapter:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2a1c9bf0f34df4985b0f86076fd5dd9d\transformed\cursoradapter-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [android.arch.lifecycle:runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e847ff002a6ae0edde05d2c7b23526aa\transformed\runtime-1.1.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:documentfile:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\18cff65c999935eb0190699b9d932c33\transformed\documentfile-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:localbroadcastmanager:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f07c6b3f0e237453a4acc37fbe8e722\transformed\localbroadcastmanager-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:print:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\197abb9eb58a8e0d01372035003c92a6\transformed\print-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [android.arch.lifecycle:viewmodel:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d1c14ed9d12cf6fc5db788b3e0a12ae2\transformed\viewmodel-1.1.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:interpolator:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc50f2ed5a6bc63bd61830e2792249ea\transformed\interpolator-28.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [android.arch.lifecycle:livedata:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\68b0bddc40886219e188fd5250bc759f\transformed\livedata-1.1.1\AndroidManifest.xml:17:1-22:12
MERGED from [android.arch.lifecycle:livedata-core:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\fc12cf07d6e73a7f6ec580cb767a6220\transformed\livedata-core-1.1.1\AndroidManifest.xml:17:1-22:12
MERGED from [android.arch.core:runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ca327b899be1ce97a1642226f3df8380\transformed\runtime-1.1.1\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:4:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:4:22-78
application
ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:6:5-37:19
INJECTED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:6:5-37:19
MERGED from [com.android.support:design:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8270adb3dcd9272142798ba9958ab579\transformed\design-28.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.android.support:design:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8270adb3dcd9272142798ba9958ab579\transformed\design-28.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.android.support.constraint:constraint-layout:1.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\868eef2cd99d9b153fbf31a49138b958\transformed\constraint-layout-1.1.3\AndroidManifest.xml:9:5-20
MERGED from [com.android.support.constraint:constraint-layout:1.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\868eef2cd99d9b153fbf31a49138b958\transformed\constraint-layout-1.1.3\AndroidManifest.xml:9:5-20
MERGED from [com.android.support:support-compat:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b401c224dd8ca0c8f00c38b06cf5788f\transformed\support-compat-28.0.0\AndroidManifest.xml:22:5-94
MERGED from [com.android.support:support-compat:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b401c224dd8ca0c8f00c38b06cf5788f\transformed\support-compat-28.0.0\AndroidManifest.xml:22:5-94
MERGED from [com.android.support:versionedparcelable:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe7de84392061693ae5f87ceab83d73e\transformed\versionedparcelable-28.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.android.support:versionedparcelable:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe7de84392061693ae5f87ceab83d73e\transformed\versionedparcelable-28.0.0\AndroidManifest.xml:22:5-23:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [com.android.support:support-compat:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b401c224dd8ca0c8f00c38b06cf5788f\transformed\support-compat-28.0.0\AndroidManifest.xml:22:18-91
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:11:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:9:9-30
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:10:9-54
	android:icon
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:8:9-39
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:7:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:12:9-40
activity#com.example.bottomnavigationview.Recipe
ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:13:9-14:49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:14:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:13:19-41
activity#com.example.bottomnavigationview.WelcomeActivty
ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:15:9-22:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:16:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:15:19-49
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:17:13-21:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:18:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:18:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:20:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:20:27-74
activity#com.example.bottomnavigationview.MainActivity
ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:23:9-24:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:24:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:23:19-47
activity#com.example.bottomnavigationview.BuyActivity
ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:25:9-26:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:26:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:25:19-46
activity#com.example.bottomnavigationview.PreVegetables
ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:27:9-28:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:28:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:27:19-48
activity#com.example.bottomnavigationview.BuyMoney
ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:29:9-30:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:30:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:29:19-43
activity#com.example.bottomnavigationview.FireForumListActivity2
ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:31:9-32:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:32:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:31:19-57
activity#com.example.bottomnavigationview.FireDiscActivity2
ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:33:9-34:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:34:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:33:19-52
activity#com.example.bottomnavigationview.VGanalysis
ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:35:9-36:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:36:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml:35:19-45
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml
MERGED from [com.android.support:design:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8270adb3dcd9272142798ba9958ab579\transformed\design-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:design:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8270adb3dcd9272142798ba9958ab579\transformed\design-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:appcompat-v7:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4973f91cd2444a4abd2089723a7e5dc9\transformed\appcompat-v7-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:appcompat-v7:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4973f91cd2444a4abd2089723a7e5dc9\transformed\appcompat-v7-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support.constraint:constraint-layout:1.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\868eef2cd99d9b153fbf31a49138b958\transformed\constraint-layout-1.1.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.support.constraint:constraint-layout:1.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\868eef2cd99d9b153fbf31a49138b958\transformed\constraint-layout-1.1.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.support:multidex:1.0.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\bbd9ec28dd32ea78e0900a1ea7d9bc6c\transformed\multidex-1.0.3\AndroidManifest.xml:20:5-43
MERGED from [com.android.support:multidex:1.0.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\bbd9ec28dd32ea78e0900a1ea7d9bc6c\transformed\multidex-1.0.3\AndroidManifest.xml:20:5-43
MERGED from [com.android.support:cardview-v7:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a7298b63126ca22b56a14d4e307079ac\transformed\cardview-v7-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:cardview-v7:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a7298b63126ca22b56a14d4e307079ac\transformed\cardview-v7-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-fragment:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebd18f75b9a3b0d73784dd6a5cbc30ef\transformed\support-fragment-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-fragment:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebd18f75b9a3b0d73784dd6a5cbc30ef\transformed\support-fragment-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:animated-vector-drawable:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3fae80cda322ff85344e8c99f2ac9c5\transformed\animated-vector-drawable-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:animated-vector-drawable:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3fae80cda322ff85344e8c99f2ac9c5\transformed\animated-vector-drawable-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:recyclerview-v7:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\75fa7e361cf1fa871ae34561b272908c\transformed\recyclerview-v7-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:recyclerview-v7:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\75fa7e361cf1fa871ae34561b272908c\transformed\recyclerview-v7-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-core-ui:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\820ed1c3d2f0c9380d113a103fb22ade\transformed\support-core-ui-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-core-ui:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\820ed1c3d2f0c9380d113a103fb22ade\transformed\support-core-ui-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-core-utils:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bac4bf448ecaf23f82c8969bbd354931\transformed\support-core-utils-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-core-utils:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bac4bf448ecaf23f82c8969bbd354931\transformed\support-core-utils-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-vector-drawable:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a8fb577233d540612a6dcc52cc13314\transformed\support-vector-drawable-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-vector-drawable:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a8fb577233d540612a6dcc52cc13314\transformed\support-vector-drawable-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:transition:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa87da10e9530b3689a72358af980e44\transformed\transition-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:transition:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa87da10e9530b3689a72358af980e44\transformed\transition-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:loader:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8c24645226d7261301ca11338088ba59\transformed\loader-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:loader:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8c24645226d7261301ca11338088ba59\transformed\loader-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:viewpager:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7caf09b9e32c19af8bc0db15aa107d83\transformed\viewpager-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:viewpager:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7caf09b9e32c19af8bc0db15aa107d83\transformed\viewpager-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:coordinatorlayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f29d80ca58f1f9b594bfd54574416d70\transformed\coordinatorlayout-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:coordinatorlayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f29d80ca58f1f9b594bfd54574416d70\transformed\coordinatorlayout-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:drawerlayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a0002004a593a9099acf9a4068e806c\transformed\drawerlayout-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:drawerlayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a0002004a593a9099acf9a4068e806c\transformed\drawerlayout-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:slidingpanelayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\12278b8824a007d7c0d94cd6b99512f2\transformed\slidingpanelayout-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:slidingpanelayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\12278b8824a007d7c0d94cd6b99512f2\transformed\slidingpanelayout-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:customview:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3b419f68d713d52fa704e9f28db1c3dd\transformed\customview-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:customview:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3b419f68d713d52fa704e9f28db1c3dd\transformed\customview-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:swiperefreshlayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62212203febe87e0c44738abb5569c8e\transformed\swiperefreshlayout-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:swiperefreshlayout:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\62212203febe87e0c44738abb5569c8e\transformed\swiperefreshlayout-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:asynclayoutinflater:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a18a0e7a8c82281bb74b0acfa9b21599\transformed\asynclayoutinflater-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:asynclayoutinflater:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a18a0e7a8c82281bb74b0acfa9b21599\transformed\asynclayoutinflater-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-compat:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b401c224dd8ca0c8f00c38b06cf5788f\transformed\support-compat-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-compat:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b401c224dd8ca0c8f00c38b06cf5788f\transformed\support-compat-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:versionedparcelable:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe7de84392061693ae5f87ceab83d73e\transformed\versionedparcelable-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:versionedparcelable:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe7de84392061693ae5f87ceab83d73e\transformed\versionedparcelable-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:cursoradapter:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2a1c9bf0f34df4985b0f86076fd5dd9d\transformed\cursoradapter-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:cursoradapter:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2a1c9bf0f34df4985b0f86076fd5dd9d\transformed\cursoradapter-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e847ff002a6ae0edde05d2c7b23526aa\transformed\runtime-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e847ff002a6ae0edde05d2c7b23526aa\transformed\runtime-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:documentfile:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\18cff65c999935eb0190699b9d932c33\transformed\documentfile-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:documentfile:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\18cff65c999935eb0190699b9d932c33\transformed\documentfile-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:localbroadcastmanager:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f07c6b3f0e237453a4acc37fbe8e722\transformed\localbroadcastmanager-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:localbroadcastmanager:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f07c6b3f0e237453a4acc37fbe8e722\transformed\localbroadcastmanager-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:print:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\197abb9eb58a8e0d01372035003c92a6\transformed\print-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:print:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\197abb9eb58a8e0d01372035003c92a6\transformed\print-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:viewmodel:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d1c14ed9d12cf6fc5db788b3e0a12ae2\transformed\viewmodel-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:viewmodel:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d1c14ed9d12cf6fc5db788b3e0a12ae2\transformed\viewmodel-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:interpolator:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc50f2ed5a6bc63bd61830e2792249ea\transformed\interpolator-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.support:interpolator:28.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc50f2ed5a6bc63bd61830e2792249ea\transformed\interpolator-28.0.0\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:livedata:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\68b0bddc40886219e188fd5250bc759f\transformed\livedata-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:livedata:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\68b0bddc40886219e188fd5250bc759f\transformed\livedata-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:livedata-core:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\fc12cf07d6e73a7f6ec580cb767a6220\transformed\livedata-core-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:livedata-core:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\fc12cf07d6e73a7f6ec580cb767a6220\transformed\livedata-core-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [android.arch.core:runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ca327b899be1ce97a1642226f3df8380\transformed\runtime-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [android.arch.core:runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ca327b899be1ce97a1642226f3df8380\transformed\runtime-1.1.1\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\AndroidManifest.xml
