package com.example.bottomnavigationview;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.app.Fragment;
import android.support.v4.app.ListFragment;
import android.util.Base64;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.SimpleAdapter;
import android.widget.TextView;
import android.widget.Toast;
import android.content.SharedPreferences;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import com.google.firebase.database.DatabaseReference;
import com.google.firebase.database.FirebaseDatabase;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;



public class FavoritesFragment  extends Fragment {

    private FirebaseAnalytics mFirebaseAnalytics;  //~~~~~取得Firebase服務實體~~~~~~~~

    TextView message;
    EditText nickname;
    SharedPreferences nameSetting;
    Button btenterFire;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View v = inflater.inflate(R.layout.fragment_favorites, container, false);
        return v;
    }


    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        // Obtain the FirebaseAnalytics instance.

        //~~~~~~~~~~~~~~~重要~~~~~~~~~在物件導向城市中,getInstance()方法用來取得實例(近物件的意思new 類別創建物件意思 )

        // FirebaseDatabase.getInstance().setPersistenceEnabled(true); //~~~~~~~~~~~取得 FirebaseDatabase然後進行設定~~~~~~~~~~~~~~~~~~~~~~~~~~~在安卓开发中，要创建一个类的对象常用类名.GetInstance()而不用new的方法.
        //啟用磁盤持久性使我們的應用程序即使在應用程序重新啟動後也能保持其所有狀態。我們只需一行代碼即可啟用磁盤持久性，.setPersistenceEnabled(true)可以避免程式崩潰

        mFirebaseAnalytics = FirebaseAnalytics.getInstance(getActivity());
        //匿名登入
        FirebaseAuth.getInstance().signInAnonymously();  //~~~~~~~~取得 FirebaseAuth才能用.signInAnonymously()方法匿名登入

        message = (TextView) getActivity().findViewById(R.id.msg);
        nickname = (EditText) getActivity().findViewById(R.id.nickname);

        btenterFire = (Button) getActivity().findViewById(R.id.btenterFire);

        //若能取得之前用過的 nickname 就把它放入輸入欄
        nameSetting = getActivity().getSharedPreferences("nameSetting", 0);
        nickname.setText(nameSetting.getString("name", ""));



        //===============重要=================Fragment中，無法使用內建OnClick方法，所以監聽OnClickListener
        btenterFire.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {


                //取得輸入的暱稱，並限制至少需兩個字元，否則給使用者錯誤訊息, 並暫停處理
                final String nicknameStrig = nickname.getText().toString();
                if (nicknameStrig.length() < 1) {
                    Toast.makeText(getActivity(), "錯誤:暱稱至少兩字元", Toast.LENGTH_SHORT).show();
                    message.setText("錯誤:暱稱至少兩字元");
                    return;
                }

                // 檢查是否登入成功
                FirebaseUser user = FirebaseAuth.getInstance().getCurrentUser();
                if (user != null) {
                    //進入下一個 Activity
                    nameSetting.edit().putString("name", nicknameStrig).commit();//getSharedPreferences()方法，呼叫edit()方法取得編輯器物件，
                    // 此時使用匿名方式呼叫Editor的putString()方法將nicknaeString字串的內容寫入設定檔，資料標籤為”name”。
                    // 最後必須呼叫commit()方法，此時資料才真正寫入到設定檔中。

                    // 如果登入成功就用把帳號存入SharedPreferences()中


                    Intent intent = new Intent();            //=============================================Intent
                    intent.setClass(getActivity(), FireForumListActivity2.class);
                    startActivity(intent);

//~~~~~~~~~~~~~~~取得登入Firebase後使用者名稱的存放路徑(Uid根參照)~~~~~~~~~~~~~~~
                    DatabaseReference onlineRef = FirebaseDatabase
                            .getInstance()
                            .getReference("forum/online/" + FirebaseAuth.getInstance().getCurrentUser().getUid());//~~~~~~~~Uid為亂碼,匿名登入產生~~~~~~~~~~~~

                    //~~~~~~~~~~~在根參照下子目錄節點"forum/online/"給予登入名稱
                    onlineRef.setValue(nicknameStrig);
                    onlineRef.onDisconnect().removeValue();


                } else {
                    Toast.makeText(getActivity(), "錯誤:無法成功連線", Toast.LENGTH_SHORT).show();
                    message.setText("錯誤:錯誤:無法成功連線");
                    return;
                }
            }
        });
    }
}

















