{"logs": [{"outputFile": "com.example.bottomnavigationview.app-mergeReleaseResources-10:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b401c224dd8ca0c8f00c38b06cf5788f\\transformed\\support-compat-28.0.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2926", "endColumns": "100", "endOffsets": "3022"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\4973f91cd2444a4abd2089723a7e5dc9\\transformed\\appcompat-v7-28.0.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,333,440,526,630,752,834,915,1006,1099,1195,1289,1390,1483,1578,1677,1768,1859,1945,2048,2153,2251,2356,2469,2572,2745,2842", "endColumns": "118,108,106,85,103,121,81,80,90,92,95,93,100,92,94,98,90,90,85,102,104,97,104,112,102,172,96,83", "endOffsets": "219,328,435,521,625,747,829,910,1001,1094,1190,1284,1385,1478,1573,1672,1763,1854,1940,2043,2148,2246,2351,2464,2567,2740,2837,2921"}}]}]}