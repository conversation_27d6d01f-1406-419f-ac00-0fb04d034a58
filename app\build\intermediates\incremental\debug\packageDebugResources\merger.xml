<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res"><file name="bill" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\bill.jpg" qualifiers="" type="drawable"/><file name="button_green_round" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\button_green_round.xml" qualifiers="" type="drawable"/><file name="button_red_round" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\button_red_round.xml" qualifiers="" type="drawable"/><file name="cookbear" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\cookbear.jpg" qualifiers="" type="drawable"/><file name="cookbear2" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\cookbear2.jpg" qualifiers="" type="drawable"/><file name="ic_close_black_24dp" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\ic_close_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_error_outline_black_24dp" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\ic_error_outline_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_favorite_black_24dp" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\ic_favorite_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_home_black_24dp" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\ic_home_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launch_black_24dp" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\ic_launch_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_local_bar_black_24dp" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\ic_local_bar_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_search_black_24dp" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\ic_search_black_24dp.xml" qualifiers="" type="drawable"/><file name="meat" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\meat.jpg" qualifiers="" type="drawable"/><file name="outside" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\outside.xml" qualifiers="" type="drawable"/><file name="seafood" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\seafood.jpg" qualifiers="" type="drawable"/><file name="speech_bubble" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\speech_bubble.png" qualifiers="" type="drawable"/><file name="takepic" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\takepic.png" qualifiers="" type="drawable"/><file name="vege1" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\vege1.jpg" qualifiers="" type="drawable"/><file name="vegetable" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\vegetable.jpg" qualifiers="" type="drawable"/><file name="welcom" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable\welcom.jpg" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="activity_buy" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\layout\activity_buy.xml" qualifiers="" type="layout"/><file name="activity_buymoney" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\layout\activity_buymoney.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_pre_vegetables" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\layout\activity_pre_vegetables.xml" qualifiers="" type="layout"/><file name="activity_recipe" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\layout\activity_recipe.xml" qualifiers="" type="layout"/><file name="activity_vganalysis" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\layout\activity_vganalysis.xml" qualifiers="" type="layout"/><file name="activity_welcome" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\layout\activity_welcome.xml" qualifiers="" type="layout"/><file name="disc2" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\layout\disc2.xml" qualifiers="" type="layout"/><file name="disc_content2" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\layout\disc_content2.xml" qualifiers="" type="layout"/><file name="epic_popup_negtive" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\layout\epic_popup_negtive.xml" qualifiers="" type="layout"/><file name="epic_popup_positive" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\layout\epic_popup_positive.xml" qualifiers="" type="layout"/><file name="fireforulist2" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\layout\fireforulist2.xml" qualifiers="" type="layout"/><file name="forum_listitem2" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\layout\forum_listitem2.xml" qualifiers="" type="layout"/><file name="fragment_favorites" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\layout\fragment_favorites.xml" qualifiers="" type="layout"/><file name="fragment_home" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\layout\fragment_home.xml" qualifiers="" type="layout"/><file name="fragment_search" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\layout\fragment_search.xml" qualifiers="" type="layout"/><file name="item" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\layout\item.xml" qualifiers="" type="layout"/><file name="item1" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\layout\item1.xml" qualifiers="" type="layout"/><file name="item2money" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\layout\item2money.xml" qualifiers="" type="layout"/><file name="bottom_navigation" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\menu\bottom_navigation.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\mipmap-hdpi\ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="welcom2" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\mipmap-hdpi\welcom2.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\mipmap-mdpi\ic_launcher_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\mipmap-xhdpi\ic_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\values\colors.xml" qualifiers=""><color name="colorPrimary">#848cb7</color><color name="colorPrimaryDark">#6d749f</color><color name="colorRed">#ff3943</color><color name="colorRedDark">#801b20</color><color name="colorGreenLight">#abffc0</color><color name="colorGreen">#247b37</color><color name="colorGreen1">#3eb959</color><color name="colorWhite">#ffffff</color><color name="colorAccent">#FF4081</color></file><file path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">MyVG0809</string><string-array name="array">
        <item name="Batman"/>
        <item name="Superman"/>
        <item name="Flash"/>
        <item name="Thor"/>
    </string-array><string-array name="items">
        <item>肉類</item>
        <item>海鮮類</item>
        <item>蔬果類</item>
        <item>奶蛋類</item>
        <item>麵食</item>
        <item>調味類</item>
        <item>熟食</item>
        <item>其它</item>
    </string-array><string-array name="place">
        <item>冷藏</item>
        <item>冷凍</item>
        <item>蔬果層</item>
        <item>門側</item>
        <item>其它</item>


    </string-array><string-array name="weekday">
        <item>星期日</item>
        <item>星期一</item>
        <item>星期二</item>
        <item>星期三</item>
        <item>星期四</item>
        <item>星期五</item>
        <item>星期六</item>
    </string-array><string-array name="items3">
        <item>1份</item>
        <item>2份</item>
        <item>3份</item>
        <item>4份</item>
    </string-array><string name="positive_popup_text_default">You have successfully completed the challenge. Keep it up to earn more points.</string><string name="positive_popup_title_default">Like a boss!</string><string name="negative_popup_text_defaulte">You have failed to complete the challenge. So close! Wanna have another go at it?</string><string name="negative_popup_title_defaulte">Awww.... Snap!</string></file><file path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Vege\MyVG(20200201)\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>