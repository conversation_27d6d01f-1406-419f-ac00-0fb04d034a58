<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.bottomnavigationview">

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/welcom2"
        android:label="Grace"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity android:name=".Recipe"></activity>
        <activity android:name=".WelcomeActivty">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".MainActivity" />
        <activity android:name=".BuyActivity" />
        <activity android:name=".PreVegetables" />
        <activity android:name=".BuyMoney" />
        <activity android:name=".FireForumListActivity2" />
        <activity android:name=".FireDiscActivity2" />
        <activity android:name=".VGanalysis" />
    </application>

</manifest>