<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/welcom2"
        android:label="Grace"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity android:name=".Recipe"
            android:exported="false"></activity>
        <activity android:name=".WelcomeActivty"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".MainActivity"
            android:exported="false" />
        <activity android:name=".BuyActivity"
            android:exported="false" />
        <activity android:name=".PreVegetables"
            android:exported="false" />
        <activity android:name=".BuyMoney"
            android:exported="false" />
        <activity android:name=".FireForumListActivity2"
            android:exported="false" />
        <activity android:name=".FireDiscActivity2"
            android:exported="false" />
        <activity android:name=".VGanalysis"
            android:exported="false" />
    </application>

</manifest>