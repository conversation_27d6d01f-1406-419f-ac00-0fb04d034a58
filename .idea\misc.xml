<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <expanded-state>
          <State />
          <State>
            <id>Android</id>
          </State>
          <State>
            <id>CorrectnessLintAndroid</id>
          </State>
          <State>
            <id>LintAndroid</id>
          </State>
          <State>
            <id>PerformanceLintAndroid</id>
          </State>
          <State>
            <id>SecurityLintAndroid</id>
          </State>
          <State>
            <id>UsabilityLintAndroid</id>
          </State>
        </expanded-state>
      </profile-state>
    </entry>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" default="true" project-jdk-name="corretto-1.8" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/build/classes" />
  </component>
  <component name="ProjectType">
    <option name="id" value="Android" />
  </component>
</project>