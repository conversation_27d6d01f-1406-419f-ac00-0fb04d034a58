plugins {
    id 'com.android.application'
    // id 'com.google.gms.google-services' // Temporarily disabled
}

android {
    namespace "com.example.bottomnavigationview"
    compileSdk 34

    defaultConfig {
        applicationId "com.example.bottomnavigationview"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true
        vectorDrawables.useSupportLibrary = true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    packagingOptions {
        pickFirst 'META-INF/*'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    lint {
        abortOnError false
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    // Support Library (Legacy)
    implementation 'com.android.support:appcompat-v7:28.0.0'
    implementation 'com.android.support.constraint:constraint-layout:1.1.3'
    implementation 'com.android.support:multidex:1.0.3'
    implementation 'com.android.support:cardview-v7:28.0.0'
    implementation 'com.android.support:design:28.0.0'

    // Firebase (Temporarily disabled for compatibility)
    // implementation 'com.google.firebase:firebase-analytics:17.6.0'
    // implementation 'com.google.firebase:firebase-auth:19.4.0'
    // implementation 'com.google.firebase:firebase-database:19.7.0'
    // implementation 'com.google.firebase:firebase-firestore:21.7.1'
    // implementation 'com.google.firebase:firebase-messaging:20.3.0'
    // implementation 'com.google.firebase:firebase-storage:19.2.2'
    // implementation 'com.google.firebase:firebase-perf:19.1.1'

    // Testing
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'
}