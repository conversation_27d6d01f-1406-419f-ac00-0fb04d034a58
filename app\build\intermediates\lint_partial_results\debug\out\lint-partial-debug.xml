<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.1.4" type="partial_results">
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.example.bottomnavigationview.WelcomeActivty"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.mipmap.ic_launcher"
            file="${:app*debug*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="17"
            endOffset="276"/>
        <location id="R.string.app_name"
            file="${:app*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="2"
            column="13"
            startOffset="25"
            endLine="2"
            endColumn="28"
            endOffset="40"/>
        <location id="R.drawable.cookbear"
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/cookbear.jpg"/>
        <location id="R.drawable.ic_launch_black_24dp"
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/ic_launch_black_24dp.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="9"
            endColumn="10"
            endOffset="427"/>
        <location id="R.layout.item"
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/item.xml"
            line="8"
            column="5"
            startOffset="56"
            endLine="29"
            endColumn="22"
            endOffset="1019"/>
        <entry
            name="model"
            string="array[weekday(D),items3(U),array(U),place(U),items(U)],color[colorRedDark(U),colorPrimary(U),colorPrimaryDark(U),colorRed(U),colorGreen(U),cardview_dark_background(R),colorWhite(U),colorAccent(U),colorGreenLight(U),colorGreen1(U)],drawable[seafood(U),ic_favorite_black_24dp(D),bill(U),ic_launch_black_24dp(D),speech_bubble(D),vege1(U),button_red_round(U),vegetable(U),cookbear2(U),ic_local_bar_black_24dp(U),takepic(U),ic_close_black_24dp(U),welcom(U),button_green_round(U),ic_launcher_foreground(U),ic_launcher_background(U),outside(U),meat(U),ic_home_black_24dp(U),ic_error_outline_black_24dp(U),ic_launcher_foreground_1(R),ic_search_black_24dp(U),cookbear(D)],id[msg(U),BtPice(U),subject(U),fourmList(U),BtPre(U),imageView(D),etRemarks(U),imv2(U),textViw21(D),spinner_kind(U),buydate(U),closePopupPositiveImg(U),welcome(D),imageie4(D),editText1(D),button4(D),textView6(D),button5(D),bottom_navigation(U),textView7(U),button2(D),button3(U),btrefresh(U),textView2(U),textView3(D),button6(D),textView4(D),kind(U),textView5(U),disctime(U),spinner_part(U),editText(D),btnAccept(U),btenterFire(U),lastUpdate(U),imageview(D),guideline(U),disccontent(U),buyID(U),name1(D),nav_home(U),btSure(D),messageTv(U),btnRetry(D),textView19(D),textView18(D),positivePopupBtn(U),textView17(D),part(U),textView13(D),button(D),spinner_serving(U),finekind(U),imv(U),savedate(U),nickname(U),guideline2(U),imageButton(D),textView(U),negativePopupBtn(U),spinner_site(U),BtBuy(U),nav_favorite(U),spinner_fine_kind(U),imageView4(D),photo(U),lastUpdateUserNickname(U),message(U),texViw21(D),imageView1(D),imageView3(D),titleTv(U),textView21(D),imageView2(U),textView20(D),imageVie4(D),disclist(U),nav_search(U),fragment_container(U),BtCook(U),closePopupNegativeImg(U)],layout[disc_content2(U),item(D),activity_pre_vegetables(U),item1(U),fragment_home(U),item2money(U),activity_welcome(U),fireforulist2(U),fragment_favorites(U),activity_buymoney(U),activity_recipe(U),activity_buy(U),disc2(U),activity_main(U),fragment_search(U),activity_vganalysis(U),epic_popup_positive(U),epic_popup_negtive(U),forum_listitem2(U)],menu[bottom_navigation(U)],mipmap[ic_launcher_round(U),welcom2(U),ic_launcher(D)],string[negative_popup_title_defaulte(U),negative_popup_text_defaulte(U),positive_popup_title_default(U),app_name(D),positive_popup_text_default(U)],style[Widget_AppCompat_Button_ButtonBar_AlertDialog(R),Theme_AppCompat_Light_NoActionBar(R),TextAppearance_AppCompat_Display1(R),AppTheme(U)],sample[date/mmddyy(R)];15^5,18^e,1a^e,1c^9,1d^23,1f^b^a,22^b,77^42^4b^39^5d,78^67,7a^95^c^19^6,7b^5e^4a,7c^95^c^7,7d^11,7e^3d,7f^60^17^5d,80^24,82^1f^19^97^4^7^1^3,83^3b^5d^28^69,84^38^8a,85^8b^7^c^93,86^24,87^1a^d^90^18^92^1c^c,88^1a^8^8e^b^22^8f^15^c,89^1b,8a^21,8b^1e^1d,8d^1e^1d,96^94^6^7^c;;;"/>
        <location id="R.array.weekday"
            file="${:app*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="30"
            column="19"
            startOffset="725"
            endLine="30"
            endColumn="33"
            endOffset="739"/>
        <location id="R.drawable.speech_bubble"
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/speech_bubble.png"/>
        <location id="R.drawable.ic_favorite_black_24dp"
            file="${:app*debug*sourceProvider*0*resDir*0}/drawable/ic_favorite_black_24dp.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="9"
            endColumn="10"
            endOffset="468"/>
    </map>

</incidents>
