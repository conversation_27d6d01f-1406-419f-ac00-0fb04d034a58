package com.example.bottomnavigationview;

import android.app.ListActivity;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.util.Base64;
import android.view.View;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.SimpleAdapter;
import android.widget.Toast;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static com.example.bottomnavigationview.BuyActivity.DB_NAME;
import static com.example.bottomnavigationview.BuyActivity.TB_NAME;


public class PreVegetables extends ListActivity{
    private String[] mListName = { };


    ListView list=null ;
    ArrayList<Map<String, Object>> mData = new ArrayList<Map<String, Object>>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_pre_vegetables);


        getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_FULLSCREEN); // Toolbar隱藏  共兩行  2/2
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);//~~~~~~~~~~去除頂列(充電等狀態)~~~~~~~~~~~~~~~~~~~


           list=getListView();

           getListView().setEmptyView(findViewById(android.R.id.list));

           mData = new ArrayList<Map<String, Object>>();



                GetCommonUsers();  //获取本地数据库Role表中用户信息


                SimpleAdapter adapter= new SimpleAdapter(this, mData, R.layout.item1,new String[] { "image", "kind", "finekind", "buydate", "part", "_id","savedate"  }, new int[] { R.id.photo, R.id.kind,R.id.finekind,R.id.buydate,R.id.part,R.id.buyID,R.id.savedate});

                adapter.setViewBinder(new SimpleAdapter.ViewBinder(){
                    public boolean setViewValue(View view, Object data,String textRepresentation){
                        if(view instanceof ImageView && data instanceof Bitmap){
                            ImageView iv = (ImageView) view;
                            iv.setImageBitmap((Bitmap)data);
                            return true;
                        }else{
                            return false; }  }  });

                setListAdapter(adapter);

                list.setOnItemClickListener(new AdapterView.OnItemClickListener() {

                    //处理点击事件

                    public void onItemClick(AdapterView<?> adapterView, View view, int position, long id) {

                        Toast.makeText(PreVegetables.this, "您選擇了 " +  mListName[position],

                                Toast.LENGTH_SHORT).show();

                    }

                });



            }


            protected void GetCommonUsers()  {

                SQLiteDatabase db = openOrCreateDatabase(DB_NAME, Context.MODE_PRIVATE, null);
                //Cursor cursor = db.rawQuery("SELECT * FROM hotlist GROUP BY finekind HAVING count(*)>0 ",null);
                // Cursor cursor = db.rawQuery("SELECT * FROM hotlist where buyprice=23",null);

                //Cursor cursor = db.rawQuery("SELECT * FROM hotlist where _id=?",new String[] {"3"}); 篩選成功---------單一
                // Cursor cursor = db.rawQuery("SELECT * FROM hotlist where buyprice=23",null);篩選成功--------多筆
                Cursor cursor = db.rawQuery("SELECT * FROM "+ TB_NAME,null);
                int num = cursor.getCount();

                if (num > 0) {
                    mListName = new String[num];    //宣告陣列數目~~~~~~~~~~


                    int index = 0;
                    String txvKind = "";  //臨時字串變數,放置集合用
                    String txvFineKind= "";
                    String ebuydate= "";
                    String esavedate= "";
                    String eID= "";
                    String txvPart;

                    Bitmap photo = null;
                    byte[] imgArr2=null;

                    // 必须使用moveToFirst方法将记录指针移动到第1条记录的位置

                    cursor.moveToFirst();

                    do {            ////~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~迴圈

                        txvKind = cursor.getString(cursor.getColumnIndex("kind"));
                        mListName[index++] = txvKind; //臨時字串變數,放置集合用

                        txvFineKind = cursor.getString(cursor.getColumnIndex("finekind"));
                        ebuydate = cursor.getString(cursor.getColumnIndex("buydate"));
                        esavedate = cursor.getString(cursor.getColumnIndex("savedate"));
                        eID = cursor.getString(cursor.getColumnIndex("_id"));
                        txvPart= cursor.getString(cursor.getColumnIndex("part"));

                        //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~取得image
                        imgArr2 = Base64.decode(cursor.getString(9), Base64.DEFAULT);


                        Map<String, Object> item = new HashMap<String, Object>();   //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~用Map建立集合 item

                        if( imgArr2 !=null && imgArr2.length > 0){

                            photo = BitmapFactory.decodeByteArray(imgArr2, 0, imgArr2.length);

                            item.put("image", photo); //~~~~~~~~~~~~~~迴圈把臨時變數photo放入 image,  並把image放入item~~~~~~~~~~~~~~~
                        }
                        else{ //如果用户没有上传头像资源，就用默认图标代替
                            item.put("image", R.drawable.takepic);
                    }

                        item.put("kind", txvKind);
                        item.put("finekind", txvFineKind);
                        item.put("buydate", ebuydate);
                        item.put("savedate", esavedate);
                        item.put("_id", eID);
                        item.put("part", txvPart);


                        mData.add(item);

                    } while (cursor.moveToNext());  //~~~~~~~~~~~~~~~還有下一行的話就Run do{}迴圈

                }

            }

            }












