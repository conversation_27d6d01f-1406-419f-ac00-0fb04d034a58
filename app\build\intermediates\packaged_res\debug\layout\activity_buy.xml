<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_margin="10dp"
    tools:context=".BuyActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="20dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@drawable/outside">

            <Button
                android:id="@+id/button5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="首頁" />

            <Button
                android:id="@+id/button4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="總清單" />

            <Button
                android:id="@+id/button3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="菜價分析" />

            <Button
                android:id="@+id/button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="保存期限" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_editor_absoluteX="3dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toStartOf="parent"
            tools:layout_editor_absoluteY="204dp">

            <ImageView
                android:id="@+id/imv"
                android:layout_width="80dp"
                android:layout_height="59dp"
                android:layout_marginStart="16dp"
                android:layout_marginLeft="16dp"
                android:onClick="buyphoto"
                app:layout_constraintStart_toStartOf="parent"
                app:srcCompat="@drawable/takepic"
                tools:layout_editor_absoluteY="27dp" />

            <TextView
                android:layout_width="140dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="購買日期:"
                tools:textSize="18sp" />

            <TextView
                android:id="@+id/buydate"
                android:layout_width="256dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center|center_horizontal|center_vertical"
                android:text="2019-10-08"
                android:textSize="18sp"
                tools:text="@tools:sample/date/mmddyy" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="0"
            android:orientation="horizontal">


            <ImageView
                android:id="@+id/imv2"
                android:layout_width="60dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="25dp"
                android:layout_marginTop="5dp"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="5dp" />

            <TextView
                android:layout_width="140dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="保存期期:"
                tools:textSize="18sp" />

            <TextView
                android:id="@+id/savedate"
                android:layout_width="256dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center|center_horizontal|center_vertical"
                android:text="2019-10-15"
                android:textSize="18sp"
                tools:text="@tools:sample/date/mmddyy" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:baselineAligned="true"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/textView"
                android:layout_width="163dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="種類"
                android:textAlignment="center"
                android:textSize="18sp" />

            <Spinner

                android:id="@+id/spinner_kind"
                android:layout_width="328dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:entries="@array/items"
                android:popupBackground="@color/colorPrimaryDark"
                android:saveEnabled="true"
                android:scrollbarAlwaysDrawVerticalTrack="true"
                android:scrollbarStyle="insideInset"
                android:soundEffectsEnabled="true"
                android:spinnerMode="dialog"
                android:splitMotionEvents="true" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:baselineAligned="true"
            android:orientation="horizontal">

            <TextView
                android:layout_width="163dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="子類"
                android:textAlignment="center"
                android:textSize="18sp" />

            <Spinner

                android:id="@+id/spinner_fine_kind"
                android:layout_width="328dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:popupBackground="@color/colorPrimaryDark"
                android:saveEnabled="false"
                android:scrollbarAlwaysDrawVerticalTrack="false"
                android:scrollbarStyle="insideInset"
                android:soundEffectsEnabled="true"
                android:spinnerMode="dialog"
                android:splitMotionEvents="true" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:baselineAligned="true"
            android:orientation="horizontal">

            <TextView
                android:layout_width="163dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="部位"
                android:textAlignment="center"
                android:textSize="18sp" />

            <Spinner
                android:id="@+id/spinner_part"
                android:layout_width="328dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:popupBackground="@color/colorPrimaryDark"
                android:saveEnabled="true"
                android:scrollbarAlwaysDrawVerticalTrack="false"
                android:scrollbarStyle="insideInset"
                android:soundEffectsEnabled="true"
                android:spinnerMode="dialog"
                android:splitMotionEvents="true" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:baselineAligned="true"
            android:orientation="horizontal">

            <TextView
                android:layout_width="163dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="份數"
                android:textAlignment="center"
                android:textSize="18sp" />

            <Spinner

                android:id="@+id/spinner_serving"
                android:layout_width="328dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:entries="@array/items3"
                android:popupBackground="@color/colorPrimaryDark"
                android:saveEnabled="true"
                android:scrollbarAlwaysDrawVerticalTrack="true"
                android:scrollbarStyle="insideInset"
                android:soundEffectsEnabled="true"
                android:spinnerMode="dialog"
                android:splitMotionEvents="true" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:baselineAligned="true"
            android:orientation="horizontal">

            <TextView
                android:layout_width="163dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="位置"
                android:textAlignment="center"
                android:textSize="18sp" />

            <Spinner
                android:id="@+id/spinner_site"
                android:layout_width="328dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:entries="@array/place"
                android:popupBackground="@color/colorPrimaryDark"
                android:saveEnabled="true"
                android:scrollbarAlwaysDrawVerticalTrack="true"
                android:scrollbarStyle="insideInset"
                android:soundEffectsEnabled="true"
                android:spinnerMode="dialog"
                android:splitMotionEvents="true" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="79dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/textView13"
                android:layout_width="108dp"
                android:layout_height="match_parent"
                android:layout_weight="0.2"
                android:gravity="center|center_horizontal"
                android:text="備註:"
                android:textSize="18sp"
                tools:textSize="18sp" />

            <EditText
                android:id="@+id/etRemarks"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginRight="10dp"
                android:layout_weight="1"
                android:background="@drawable/outside"
                android:ems="10"
                android:fadingEdge="horizontal"
                android:inputType="text" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/positivePopupBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="儲存" />

            <Button
                android:id="@+id/negativePopupBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="再記一筆" />
        </LinearLayout>

        <Button
            android:id="@+id/btSure"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:onClick="btSure"
            android:text="Button" />


    </LinearLayout>

</android.support.constraint.ConstraintLayout>