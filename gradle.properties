# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8 -XX:+UseParallelGC
# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
org.gradle.parallel=true

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# android.useAndroidX=true (disabled for legacy support library compatibility)
android.useAndroidX=false

# Automatically convert third-party libraries to use AndroidX
# android.enableJetifier=true (disabled for legacy support library compatibility)
android.enableJetifier=false

# Enable Gradle Daemon
org.gradle.daemon=true

# Enable Configure on demand
org.gradle.configureondemand=true

# Specify Java version for Gradle (comment out - will be set in Android Studio)
# org.gradle.java.home=C:\\Program Files\\Java\\jdk-11.0.x

# Enable Build Cache (removed - deprecated in AGP 7.0+)
# android.enableBuildCache=true


