{"logs": [{"outputFile": "com.example.bottomnavigationview.app-mergeDebugResources-10:/values-land/values-land.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\4973f91cd2444a4abd2089723a7e5dc9\\transformed\\appcompat-v7-28.0.0\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,125,196", "endColumns": "69,70,67", "endOffsets": "120,191,259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\8270adb3dcd9272142798ba9958ab579\\transformed\\design-28.0.0\\res\\values-land\\values-land.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "10", "endOffsets": "222"}, "to": {"startLines": "5", "startColumns": "4", "startOffsets": "264", "endLines": "8", "endColumns": "10", "endOffsets": "431"}}]}]}