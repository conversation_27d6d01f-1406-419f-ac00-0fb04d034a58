{"logs": [{"outputFile": "com.example.bottomnavigationview.app-mergeReleaseResources-10:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\4973f91cd2444a4abd2089723a7e5dc9\\transformed\\appcompat-v7-28.0.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,735,812,890,981,1074,1168,1265,1366,1459,1554,1651,1742,1833,1913,2025,2127,2223,2332,2433,2545,2702,2807", "endColumns": "110,105,106,89,100,114,76,77,90,92,93,96,100,92,94,96,90,90,79,111,101,95,108,100,111,156,104,79", "endOffsets": "211,317,424,514,615,730,807,885,976,1069,1163,1260,1361,1454,1549,1646,1737,1828,1908,2020,2122,2218,2327,2428,2540,2697,2802,2882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b401c224dd8ca0c8f00c38b06cf5788f\\transformed\\support-compat-28.0.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2887", "endColumns": "100", "endOffsets": "2983"}}]}]}