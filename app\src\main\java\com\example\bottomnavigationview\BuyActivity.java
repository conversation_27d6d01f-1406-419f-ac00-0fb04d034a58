package com.example.bottomnavigationview;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.app.DatePickerDialog;
import android.app.Dialog;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.support.v4.app.ActivityCompat;
import android.support.v7.app.AppCompatActivity;
import android.util.Base64;
import android.view.View;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Calendar;

public class BuyActivity extends AppCompatActivity implements View.OnClickListener, DatePickerDialog.OnDateSetListener, AdapterView.OnItemClickListener {


    static final String DB_NAME = "HotlineDB";
        static final String TB_NAME = "hotlist";
    static final String[] FROM = new String[]{"buydate", "savedate", "kind", "finekind", "part", "serving", "site", "remarks", "image"};

    Uri imgUri;
    ImageView imv,imv2;
    TextView buydate,savedate;//日期用
    Calendar c = Calendar.getInstance();//日期用
    int thisYear = c.get(Calendar.YEAR);//日期用
    int thisMonth = c.get(Calendar.MONTH)+1;//日期用
    int thisDate = c.get(Calendar.DAY_OF_MONTH);//日期用

    SQLiteDatabase db;
    Cursor cur;
    ArrayAdapter<String> adapter;
    ArrayAdapter<String> adapter2;
    ArrayAdapter<String> adapter3;


    EditText etRemarks;
    Button btSure;
    Spinner spinnerKind, spinnerFineKind, spinnerPart,spinnerServing,spinnerSite;


    Dialog epicDialog;
    Button positivePopupBtn,negativePopupBtn,btnAccept,btnRetry;
    TextView titleTv,messageTv;
    ImageView closePopupPositiveImg,closePopupNegativeImg;



    //for spinner  主類
    String[] Kind = { "肉類", "海鮮","蔬果","奶蛋","其他" };
    String[] MeatFineKind = { "豬肉", "牛肉","雞肉" };
    String[] SeafoodFineKind= { "蝦", "蛤蠣","牡蠣","魚" };
    String[] VegeFineKind={"高麗菜","白菜","韭菜","空心菜","小黃瓜"};
    String[] MilkEggFineKind={"牛奶","雞蛋","豆漿","地瓜","芋頭"};
    String[] NoodleDumpFindKind={"寬麵","細麵","水餃","餛吞"};

    //spinner part
    String[] PorkPart={"松坂","三層","豬腳","豬心","腿庫","大骨","子排","里肌肉","梅花肉","培根"};
    String[] BeafPart={"牛筋","牛肚","牛南","牛肚","牛舌","牛排"};
    String[] ChickenPart={"雞翅","雞腿","雞胸肉","雞心","雞爪"};

    private Context context;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_buy);

        context = this;


        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_NOSENSOR);
        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        imv = (ImageView) findViewById(R.id.imv);//~~~~~~~~~照相用
        imv2 = (ImageView) findViewById(R.id.imv2);


        spinnerKind=(Spinner)findViewById(R.id.spinner_kind);
        spinnerFineKind=(Spinner) findViewById(R.id.spinner_fine_kind);
        spinnerPart=(Spinner)findViewById(R.id.spinner_part);
        spinnerServing=(Spinner)findViewById(R.id.spinner_serving);
        spinnerSite=(Spinner)findViewById(R.id.spinner_site) ;

        adapter = new ArrayAdapter<String>(this, android.R.layout.simple_spinner_item,Kind );
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);

        spinnerKind.setAdapter(adapter);
        spinnerKind.setOnItemSelectedListener(selectListener);
        spinnerFineKind.setOnItemSelectedListener(selectListener);

        imv2.setImageResource(R.drawable.vege1);




        etRemarks=(EditText) findViewById(R.id.etRemarks);
        buydate = (TextView) findViewById(R.id.buydate);//日期用
        savedate=(TextView)findViewById(R.id.savedate);
        buydate.setOnClickListener(this);//日期用
        buydate.setText(Integer.toString(thisYear)+"/" + Integer.toString(thisMonth)+"/" + Integer.toString(thisDate));//日期用



        ///資料庫~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

        db=openOrCreateDatabase(DB_NAME, Context.MODE_PRIVATE,null);

        String createTable="CREATE TABLE IF NOT EXISTS " + TB_NAME + "(_id INTEGER PRIMARY KEY AUTOINCREMENT, " + "buydate VARCHAR(32), " + "savedate VARCHAR(32), " +"kind VARCHAR(32), " +"finekind VARCHAR(32), " +"part VARCHAR(32), " +"serving VARCHAR(32), " +"site VARCHAR(32), " +"remarks VARCHAR(32)," +"image VARCHAR(32)) ";
        db.execSQL(createTable);
//static final String[] FROM=new String[] {"buydate","savedate","kind","findkindt","part","serving","remarks"};//   1購買日, 2存放日, 3種類, 4子類, 5部位, 6份數, 7位置, 8備註 9.圖片

        cur=db.rawQuery("SELECT * FROM "+ TB_NAME,null);

        if(cur.getCount()==0){
            addData("20170206","20191027","肉類","豬肉","豬腳","1份","冷凍","無","  ");
            addData("20170206","20191027","海鮮","蝦","  ","2份","冷藏","無","  ");
        }

        //======================for Dialog==================================================
        epicDialog=new Dialog(this);
        positivePopupBtn=(Button)findViewById(R.id.positivePopupBtn);
        negativePopupBtn=(Button)findViewById(R.id.negativePopupBtn);
        positivePopupBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                ShowPositivePopup();

            }
        });

        negativePopupBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                ShowNegativePopup();

            }
        });

    }
    private void addData(String buydate,String savedate,String kind,String finekind,String part,String serving,String site,String remarks,String image){
        ContentValues cv=new ContentValues(9);
        cv.put(FROM[0],buydate);
        cv.put(FROM[1],savedate);
        cv.put(FROM[2],kind);
        cv.put(FROM[3],finekind);
        cv.put(FROM[4],part);
        cv.put(FROM[5],serving);
        cv.put(FROM[6],site);
        cv.put(FROM[7],remarks);
        cv.put(FROM[8],image);



        db.insert(TB_NAME,null,cv);
    }

    String FindKind[];
    String ParkKind[];

    private AdapterView.OnItemSelectedListener selectListener = new AdapterView.OnItemSelectedListener() {
        public void onItemSelected(AdapterView<?> parent, View v, int position, long id) {
            //讀取第一個下拉選單是選擇第幾個


            Spinner spinner = (Spinner) parent;
            if (spinner.getId() == R.id.spinner_kind){
                int pos = spinnerKind.getSelectedItemPosition();
            switch (pos){
                case 0:
                   FindKind=MeatFineKind;
                    spinnerPart.setVisibility(View.VISIBLE);
                    imv2.setImageResource(R.drawable.meat);
                    break;
                case 1:
                    FindKind=SeafoodFineKind;
                    spinnerPart.setVisibility(View.INVISIBLE);
                    imv2.setImageResource(R.drawable.seafood);
                    break;
                case 2:
                    FindKind=VegeFineKind;
                    spinnerPart.setVisibility(View.INVISIBLE);
                    imv2.setImageResource(R.drawable.vegetable);
                    break;
                case 3:
                    FindKind=MilkEggFineKind;
                    spinnerPart.setVisibility(View.INVISIBLE);
                    break;
                case 4:
                    FindKind=NoodleDumpFindKind;
                    spinnerPart.setVisibility(View.INVISIBLE);
                    break;
            }
            adapter2 = new ArrayAdapter<String>(context, android.R.layout.simple_spinner_item, FindKind);

            spinnerFineKind.setAdapter(adapter2);}


          else   if (spinner.getId() == R.id.spinner_fine_kind){
                int pos = spinnerFineKind.getSelectedItemPosition();
                switch (pos){
                    case 0:
                        ParkKind=PorkPart;
                        break;
                    case 1:
                        ParkKind=BeafPart;
                        break;
                    case 2:
                        ParkKind=ChickenPart;
                        break;

                }

                adapter3 = new ArrayAdapter<String>(context, android.R.layout.simple_spinner_item, ParkKind);
          spinnerPart.setAdapter(adapter3);}



               /* h.postDelayed(new Runnable() {
                    public void run() {
                        sp2.performClick();
                    }
                }, 5000);*/


        }

        public void onNothingSelected(AdapterView<?> arg0) {

        }


    };


 /*   private AdapterView.OnItemSelectedListener selectListener2 = new AdapterView.OnItemSelectedListener() {
        public void onItemSelected(AdapterView<?> parent, View v, int position, long id) {
            //讀取第一個下拉選單是選擇第幾個
            int pos = spinnerKind.getSelectedItemPosition();

            Spinner spinner = (Spinner) parent;



               if (spinner.getId() == R.id.spinner_fine_kind){
                switch (pos){
                    case 0:
                        ParkKind=PorkPart;
                        break;
                    case 1:
                        ParkKind=BeafPart;
                        break;
                    case 2:
                        ParkKind=ChickenPart;
                        break;
                }

                adapter3 = new ArrayAdapter<String>(context, android.R.layout.simple_spinner_item, ParkKind);
                spinnerPart.setAdapter(adapter3);}



                h.postDelayed(new Runnable() {
                    public void run() {
                        sp2.performClick();
                    }
                }, 5000);


        }

        public void onNothingSelected(AdapterView<?> arg0) {

        }


    };

*/



    public  void  ShowPositivePopup(){
        epicDialog.setContentView(R.layout.epic_popup_positive);
        closePopupPositiveImg=(ImageView)epicDialog.findViewById(R.id.closePopupPositiveImg);
        btnAccept=(Button)epicDialog.findViewById(R.id.btnAccept);
        titleTv=(TextView)epicDialog.findViewById(R.id.titleTv);
        messageTv=(TextView)epicDialog.findViewById(R.id.messageTv);

        closePopupPositiveImg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                epicDialog.dismiss();
            }
        });

        epicDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        epicDialog.show();

    }

    public  void  ShowNegativePopup(){
        epicDialog.setContentView(R.layout.epic_popup_negtive);
        closePopupNegativeImg=(ImageView)epicDialog.findViewById(R.id.closePopupNegativeImg);
        btnRetry=(Button)epicDialog.findViewById(R.id.btnAccept);
        titleTv=(TextView)epicDialog.findViewById(R.id.titleTv);
        messageTv=(TextView)epicDialog.findViewById(R.id.messageTv);

        closePopupNegativeImg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                epicDialog.dismiss();
            }
        });

        epicDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        epicDialog.show();

    }
//======================================Dialog=======================================





    private void update(String buydate,String savedate,String kind,String finekind,String part,String serving,String site,String remarks,String image,int id){  //~~~~~~~~~自訂新增方法(共有8個文字欄位及1個整數ID欄位)~~~~~~~~~~~~~~~~~~~~
        ContentValues cv=new ContentValues(10);

        cv.put(FROM[0],buydate);
        cv.put(FROM[1],savedate);
        cv.put(FROM[2],kind);
        cv.put(FROM[3],finekind);
        cv.put(FROM[4],part);
        cv.put(FROM[5],serving);
        cv.put(FROM[6],site);
        cv.put(FROM[7],remarks);
        cv.put(FROM[8],image);

        db.update(TB_NAME, cv, "_id="+id, null);
    }

    //買菜紀錄按下確認鈕的方法~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    public void btSure(View v){
        String kindStr= (String) spinnerKind.getSelectedItem();   //~~~~種類
        String finekindStr=(String)spinnerFineKind.getSelectedItem();    //~~~~子類
        String partStr=(String)spinnerPart.getSelectedItem();   //~~~~部位
        String remarksStr=etRemarks.getText().toString().trim();   //~~~~備註
        String siteStr=(String)spinnerSite.getSelectedItem();   //~~~~位置
        String servingStr=(String)spinnerServing.getSelectedItem();  //~~~~份數


        String buydateStr=buydate.getText().toString().trim(); //~~~~~~~~~~~取得日期
        String savedateStr=savedate.getText().toString().trim(); //~~~~~~~~~~~保存期限~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~待寫邏輯(if view=getID  if)




        //imageView_save.setDrawingCacheEnabled(true);
        //建立圖片的緩存，圖片的緩存本身就是一個Bitmap
        imv.buildDrawingCache();
        //取得緩存圖片的Bitmap檔
        Bitmap bmp1=imv.getDrawingCache();

        //轉換為圖片指定大小
        //獲得圖片的寬高
        int width = bmp1.getWidth();
        int height = bmp1.getHeight();
        // 設置想要的大小
        int newWidth = 480;
        int newHeight = 525;
        // 計算缩放比例
        float scaleWidth = ((float) newWidth) / width;
        float scaleHeight = ((float) newHeight) / height;
        // 取得想要缩放的matrix參數
        Matrix matrix = new Matrix();
        matrix.postScale(scaleWidth, scaleHeight);
        // 得到新的圖片
        Bitmap newbm = Bitmap.createBitmap(bmp1, 0, 0, width, height, matrix,true);


        // 先把 bitmap 轉成 byte
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        newbm.compress(Bitmap.CompressFormat.JPEG, 100, stream );
        byte bytes[] = stream.toByteArray();
        // Android 2.2以上才有內建Base64，其他要自已找Libary或是用Blob存入SQLite
        // 把byte變成base64
        String base64 = Base64.encodeToString(bytes, Base64.DEFAULT);




        addData(buydateStr,savedateStr,kindStr,finekindStr,partStr,servingStr,siteStr,remarksStr,base64);  //自訂新增動作 update~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


    }

    /*private  void requery() {
        cur=db.rawQuery("SELECT *  FROM " + TB_NAME, null);
        adapter.changeCursor(cur);
    }*/



    public void buyphoto(View v) {
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, 200);
        } else {
            savePhoto();
        }
    }


    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permission, int[] grantResults) {
        if (requestCode == 200) {
            if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                savePhoto();
            } else {
                Toast.makeText(this, "程式需要寫入權限才能運作", Toast.LENGTH_SHORT).show();
            }
        }
    }

    private void savePhoto() {
        imgUri = getContentResolver().insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, new ContentValues()); //插入一個"影片媒體圖片檔"的外部路徑 , 為一個新的值(getContentResolver()為取得虛擬路徑的概念)~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        Intent it = new Intent("android.media.action.IMAGE_CAPTURE"); //啟用相機~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        it.putExtra(MediaStore.EXTRA_OUTPUT, imgUri);//  附加媒體輸出路徑,路徑為imgUri
        startActivityForResult(it, 100);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {  //Intent回傳的資料進行處理,有拍照及瀏覽照片兩種情形
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK) {
            switch (requestCode) {
                case 100:  //拍照
                    Intent it = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, imgUri); //將imgUri的內容物設為系統共享媒體
                    sendBroadcast(it);  // 用廣播方式 設定為系統共享資源
                    break;
                case 101:   //瀏覽照片
                    imgUri = data.getData();
                    break;
            }

            showImg();  //顯示照片
        } else {
            Toast.makeText(this, requestCode == 100 ? "沒有拍到照片" : "沒有選取相片", Toast.LENGTH_SHORT).show();
        }
    }

    void showImg() {
        int iw, ih, vw, vh;
        boolean needRotate;
        BitmapFactory.Options option = new BitmapFactory.Options();  //Option 有點MetaData資料的感覺~~~~~~~~~~~~~~~~~~~~~~
        option.inJustDecodeBounds = true;  //設定只圖取
        try {
            BitmapFactory.decodeStream(getContentResolver().openInputStream(imgUri), null, option);//(資料路徑imgUri, null,   操作因子option    )~~~~~~~~~~~~~~~~~~~~
        } catch (IOException e) {
            Toast.makeText(this, "讀取照片時發生錯誤", Toast.LENGTH_LONG).show();
            return;
        }

        iw = option.outWidth;
        ih = option.outHeight;
        vw = imv.getWidth();
        vh = imv.getHeight();


        int scaleFactor;
        if (iw > ih) {
            needRotate = false;
            scaleFactor = Math.min(iw / vw, ih / vh);
        } else {
            needRotate = true;
            scaleFactor = Math.min(ih / vw, iw / vh);
        }

        option.inJustDecodeBounds = false;  //為了調整Scale , 設定Option可以調整了
        option.inSampleSize = scaleFactor;  //調整尺度因子

        Bitmap bmp = null;
        try {
            bmp = BitmapFactory.decodeStream(getContentResolver().openInputStream(imgUri), null, option);  //採用調整後的Option來解碼~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~bmp~~~~~~~~~~~~~~~~~~~
        } catch (IOException e) {
            Toast.makeText(this, "無法取得照片", Toast.LENGTH_LONG);
        }

        if (needRotate) {
            Matrix matrix = new Matrix();
            matrix.postRotate(90);
            bmp = Bitmap.createBitmap(bmp, 0, 0, bmp.getWidth(), bmp.getHeight(), matrix, true);
        }




        imv.setImageBitmap(bmp);




        new AlertDialog.Builder(this)
                .setTitle("圖檔資訊")
                .setMessage("圖檔URI:" + imgUri.toString() +
                        "\n原始尺寸:" + iw + "x" + ih +
                        "\n載入尺寸:" + bmp.getWidth() + "x" + bmp.getHeight() +
                        "\n顯示尺寸:" + vw + "x" + vh + (needRotate ? "(旋轉)" : " "))
                .setNeutralButton("關閉", null).show();
    }


    public void onPick(View v) {
        Intent it = new Intent(Intent.ACTION_GET_CONTENT);
        it.setType("image/*");
        startActivityForResult(it, 101);
    }









        @Override
    public void onDateSet(DatePicker view, int year, int month, int dayOfMonth) {
            buydate.setText(( year + "/" + (month + 1) + "/" + dayOfMonth));
    }

    @Override
    public void onClick(View v) {
        if (v == buydate) {
            new DatePickerDialog(this, this, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)).show();
        }

    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {

    }
}
