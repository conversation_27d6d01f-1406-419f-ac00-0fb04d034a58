http://schemas.android.com/apk/res-auto;;${\:app*debug*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*debug*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/seafood.jpg,${\:app*debug*sourceProvider*0*resDir*0}/drawable/ic_favorite_black_24dp.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/bill.jpg,${\:app*debug*sourceProvider*0*resDir*0}/drawable/ic_launch_black_24dp.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/speech_bubble.png,${\:app*debug*sourceProvider*0*resDir*0}/drawable/vege1.jpg,${\:app*debug*sourceProvider*0*resDir*0}/drawable/button_red_round.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/vegetable.jpg,${\:app*debug*sourceProvider*0*resDir*0}/drawable/cookbear2.jpg,${\:app*debug*sourceProvider*0*resDir*0}/drawable/ic_local_bar_black_24dp.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/takepic.png,${\:app*debug*sourceProvider*0*resDir*0}/drawable/ic_close_black_24dp.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/welcom.jpg,${\:app*debug*sourceProvider*0*resDir*0}/drawable/button_green_round.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable-v24/ic_launcher_foreground.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/outside.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/meat.jpg,${\:app*debug*sourceProvider*0*resDir*0}/drawable/ic_home_black_24dp.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/ic_error_outline_black_24dp.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/ic_search_black_24dp.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/cookbear.jpg,${\:app*debug*sourceProvider*0*resDir*0}/layout/fragment_favorites.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/fragment_home.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/disc2.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/forum_listitem2.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/fireforulist2.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/fragment_search.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/activity_buy.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/item1.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/item2money.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_positive.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/activity_welcome.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/activity_buymoney.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/activity_vganalysis.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/disc_content2.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/item.xml,${\:app*debug*sourceProvider*0*resDir*0}/menu/bottom_navigation.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/epic_popup_negtive.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/activity_pre_vegetables.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/activity_recipe.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/firebase,${\:app*debug*sourceProvider*0*resDir*0}/layout/firebase2,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.png,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.png,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.png,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.png,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.png,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-hdpi/welcom2.png,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.png,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.png,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.png,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.png,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.png,${\:app*debug*sourceProvider*0*resDir*0}/values/styles.xml,+array:weekday,0,V星期日,星期一,星期二,星期三,星期四,星期五,星期六,;items3,0,V1份,2份,3份,4份,;array,0,V,,,,;place,0,V冷藏,冷凍,蔬果層,門側,其它,;items,0,V肉類,海鮮類,蔬果類,奶蛋類,麵食,調味類,熟食,其它,;+color:colorPrimary,1,V"#848cb7";colorRedDark,1,V"#801b20";colorPrimaryDark,1,V"#6d749f";colorRed,1,V"#ff3943";colorGreen,1,V"#247b37";colorWhite,1,V"#ffffff";colorAccent,1,V"#FF4081";colorGreenLight,1,V"#abffc0";colorGreen1,1,V"#3eb959";+drawable:seafood,2,F;ic_favorite_black_24dp,3,F;bill,4,F;ic_launch_black_24dp,5,F;speech_bubble,6,F;vege1,7,F;button_red_round,8,F;vegetable,9,F;cookbear2,10,F;ic_local_bar_black_24dp,11,F;takepic,12,F;ic_close_black_24dp,13,F;welcom,14,F;button_green_round,15,F;ic_launcher_foreground,16,F;ic_launcher_background,17,F;outside,18,F;meat,19,F;ic_home_black_24dp,20,F;ic_error_outline_black_24dp,21,F;ic_search_black_24dp,22,F;cookbear,23,F;+id:msg,24,F;BtPice,25,F;subject,26,F;subject,26,F;subject,27,F;fourmList,28,F;BtPre,25,F;imageView,29,F;etRemarks,30,F;imv2,30,F;textViw21,29,F;spinner_kind,30,F;buydate,30,F;buydate,31,F;buydate,32,F;closePopupPositiveImg,33,F;welcome,34,F;imageie4,29,F;editText1,29,F;button4,30,F;button4,35,F;textView6,27,F;textView6,32,F;button5,30,F;button5,36,F;bottom_navigation,37,F;bottom_navigation,37,F;textView7,38,F;textView7,38,F;button2,35,F;button3,30,F;button3,26,F;button3,26,F;button3,26,F;button3,26,F;btrefresh,28,F;textView2,28,F;textView2,28,F;textView2,28,F;textView3,26,F;textView3,31,F;textView3,32,F;button6,36,F;textView4,27,F;textView4,31,F;textView4,32,F;kind,31,F;kind,32,F;textView5,38,F;textView5,38,F;textView5,38,F;textView5,38,F;textView5,38,F;textView5,38,F;textView5,38,F;textView5,38,F;textView5,31,F;textView5,32,F;disctime,38,F;spinner_part,30,F;editText,29,F;btnAccept,33,F;btenterFire,24,F;lastUpdate,27,F;imageview,24,F;guideline,25,F;guideline,25,F;guideline,25,F;guideline,25,F;guideline,25,F;disccontent,38,F;disccontent,38,F;buyID,31,F;buyID,32,F;name1,39,F;nav_home,40,F;btSure,30,F;messageTv,41,F;messageTv,33,F;btnRetry,41,F;textView19,42,F;textView18,36,F;positivePopupBtn,30,F;textView17,35,F;part,31,F;part,32,F;textView13,30,F;button,30,F;button,29,F;spinner_serving,30,F;finekind,31,F;finekind,32,F;imv,30,F;savedate,30,F;savedate,31,F;savedate,32,F;nickname,26,F;nickname,26,F;nickname,26,F;nickname,26,F;nickname,26,F;nickname,38,F;nickname,38,F;nickname,24,F;nickname,24,F;nickname,24,F;guideline2,25,F;guideline2,25,F;guideline2,25,F;guideline2,25,F;guideline2,25,F;imageButton,36,F;textView,30,F;textView,24,F;textView,24,F;textView,31,F;textView,32,F;negativePopupBtn,30,F;spinner_site,30,F;BtBuy,25,F;nav_favorite,40,F;spinner_fine_kind,30,F;imageView4,29,F;photo,39,F;photo,39,F;photo,31,F;lastUpdateUserNickname,27,F;message,26,F;message,26,F;message,26,F;texViw21,29,F;imageView1,29,F;imageView3,35,F;titleTv,41,F;titleTv,33,F;textView21,29,F;imageView2,27,F;textView20,29,F;imageVie4,29,F;disclist,26,F;nav_search,40,F;fragment_container,37,F;BtCook,25,F;closePopupNegativeImg,41,F;+layout:disc_content2,38,F;item,39,F;activity_pre_vegetables,42,F;item1,31,F;fragment_home,25,F;item2money,32,F;activity_welcome,34,F;fireforulist2,28,F;fragment_favorites,24,F;activity_buymoney,35,F;activity_recipe,43,F;activity_buy,30,F;firebase,44,F;disc2,26,F;activity_main,37,F;fragment_search,29,F;activity_vganalysis,36,F;firebase2,45,F;epic_popup_positive,33,F;epic_popup_negtive,41,F;forum_listitem2,27,F;+menu:bottom_navigation,40,F;+mipmap:ic_launcher_round,46,F;ic_launcher_round,47,F;ic_launcher_round,48,F;ic_launcher_round,49,F;ic_launcher_round,50,F;ic_launcher_round,51,F;welcom2,52,F;ic_launcher,53,F;ic_launcher,54,F;ic_launcher,55,F;ic_launcher,56,F;ic_launcher,57,F;ic_launcher,58,F;+string:negative_popup_title_defaulte,0,V"Awww.... Snap!";negative_popup_text_defaulte,0,V"You have failed to complete the challenge. So close! Wanna have another go at it?";app_name,0,V"MyVG0809";positive_popup_title_default,0,V"Like a boss!";positive_popup_text_default,0,V"You have successfully completed the challenge. Keep it up to earn more points.";+style:AppTheme,59,VDTheme.AppCompat.Light.NoActionBar,colorPrimary:@color/colorPrimary,colorPrimaryDark:@color/colorPrimaryDark,colorAccent:@color/colorAccent,;