<?xml version="1.0" encoding="UTF-8"?>
<com.android.support>
  <support-compat versions="24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <leanback-v17 versions="21.0.0,21.0.2,21.0.3,22.0.0,22.1.0,22.1.1,22.2.0,22.2.1,23.0.0,23.0.1,23.1.0,23.1.1,23.2.0,23.2.1,23.3.0,23.4.0,24.0.0-alpha1,24.0.0-alpha2,24.0.0-beta1,24.0.0,24.1.0,24.1.1,24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <recommendation versions="23.0.1,23.1.0,23.1.1,23.2.0,23.2.1,23.3.0,23.4.0,24.0.0-alpha1,24.0.0-alpha2,24.0.0-beta1,24.0.0,24.1.0,24.1.1,24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <support-tv-provider versions="26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <support-vector-drawable versions="23.2.0,23.2.1,23.3.0,23.4.0,24.0.0-alpha1,24.0.0-alpha2,24.0.0-beta1,24.0.0,24.1.0,24.1.1,24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <recyclerview-v7 versions="21.0.0,21.0.2,21.0.3,22.0.0,22.1.0,22.1.1,22.2.0,22.2.1,23.0.0,23.0.1,23.1.0,23.1.1,23.2.0,23.2.1,23.3.0,23.4.0,24.0.0-alpha1,24.0.0-alpha2,24.0.0-beta1,24.0.0,24.1.0,24.1.1,24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <preference-leanback-v17 versions="23.0.0,23.0.1,23.1.0,23.1.1,23.2.0,23.2.1,23.3.0,23.4.0,24.0.0-alpha1,24.0.0-alpha2,24.0.0-beta1,24.0.0,24.1.0,24.1.1,24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <preference-v14 versions="23.0.0,23.0.1,23.1.0,23.1.1,23.2.0,23.2.1,23.3.0,23.4.0,24.0.0-alpha1,24.0.0-alpha2,24.0.0-beta1,24.0.0,24.1.0,24.1.1,24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <percent versions="22.2.0,23.0.0,23.0.1,23.1.0,23.1.1,23.2.0,23.2.1,23.3.0,23.4.0,24.0.0-alpha1,24.0.0-alpha2,24.0.0-beta1,24.0.0,24.1.0,24.1.1,24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <support-media-compat versions="24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <cardview-v7 versions="21.0.0,21.0.2,21.0.3,22.0.0,22.1.0,22.1.1,22.2.0,22.2.1,23.0.0,23.0.1,23.1.0,23.1.1,23.2.0,23.2.1,23.3.0,23.4.0,24.0.0-alpha1,24.0.0-alpha2,24.0.0-beta1,24.0.0,24.1.0,24.1.1,24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <wearable versions="26.0.0-alpha1"/>
  <exifinterface versions="25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <support-annotations versions="19.1.0,20.0.0,21.0.0,21.0.2,21.0.3,22.0.0,22.1.0,22.1.1,22.2.0,22.2.1,23.0.0,23.0.1,23.1.0,23.1.1,23.2.0,23.2.1,23.3.0,23.4.0,24.0.0-alpha1,24.0.0-alpha2,24.0.0-beta1,24.0.0,24.1.0,24.1.1,24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <appcompat-v7 versions="18.0.0,19.0.0,19.0.1,19.1.0,20.0.0,21.0.0,21.0.2,21.0.3,22.0.0,22.1.0,22.1.1,22.2.0,22.2.1,23.0.0,23.0.1,23.1.0,23.1.1,23.2.0,23.2.1,23.3.0,23.4.0,24.0.0-alpha1,24.0.0-alpha2,24.0.0-beta1,24.0.0,24.1.0,24.1.1,24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <palette-v7 versions="21.0.0,21.0.2,21.0.3,22.0.0,22.1.0,22.1.1,22.2.0,22.2.1,23.0.0,23.0.1,23.1.0,23.1.1,23.2.0,23.2.1,23.3.0,23.4.0,24.0.0-alpha1,24.0.0-alpha2,24.0.0-beta1,24.0.0,24.1.0,24.1.1,24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <multidex-instrumentation versions="1.0.0,1.0.1,1.0.2,1.0.3"/>
  <multidex versions="1.0.0,1.0.1,1.0.2,1.0.3"/>
  <mediarouter-v7 versions="18.0.0,19.0.0,19.0.1,19.1.0,20.0.0,21.0.0,21.0.2,21.0.3,22.0.0,22.1.0,22.1.1,22.2.0,22.2.1,23.0.0,23.0.1,23.1.0,23.1.1,23.2.0,23.2.1,23.3.0,23.4.0,24.0.0-alpha1,24.0.0-alpha2,24.0.0-beta1,24.0.0,24.1.0,24.1.1,24.2.0,24.2.1,25.0.0,25.0.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-alpha4,28.0.0-alpha5,28.0.0-beta01,28.0.0"/>
  <preference-v7 versions="23.0.0,23.0.1,23.1.0,23.1.1,23.2.0,23.2.1,23.3.0,23.4.0,24.0.0-alpha1,24.0.0-alpha2,24.0.0-beta1,24.0.0,24.1.0,24.1.1,24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <support-dynamic-animation versions="25.3.0,25.3.1,25.4.0,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <support-fragment versions="24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <design versions="22.2.0,22.2.1,23.0.0,23.0.1,23.1.0,23.1.1,23.2.0,23.2.1,23.3.0,23.4.0,24.0.0-alpha1,24.0.0-alpha2,24.0.0-beta1,24.0.0,24.1.0,24.1.1,24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <transition versions="24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <customtabs versions="23.0.0,23.0.1,23.1.0,23.1.1,23.2.0,23.2.1,23.3.0,23.4.0,24.0.0-alpha1,24.0.0-alpha2,24.0.0-beta1,24.0.0,24.1.0,24.1.1,24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <support-core-ui versions="24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <gridlayout-v7 versions="13.0.0,18.0.0,19.0.0,19.0.1,19.1.0,20.0.0,21.0.0,21.0.2,21.0.3,22.0.0,22.1.0,22.1.1,22.2.0,22.2.1,23.0.0,23.0.1,23.1.0,23.1.1,23.2.0,23.2.1,23.3.0,23.4.0,24.0.0-alpha1,24.0.0-alpha2,24.0.0-beta1,24.0.0,24.1.0,24.1.1,24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <animated-vector-drawable versions="23.2.0,23.2.1,23.3.0,23.4.0,24.0.0-alpha1,24.0.0-alpha2,24.0.0-beta1,24.0.0,24.1.0,24.1.1,24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <support-core-utils versions="24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <support-v13 versions="13.0.0,18.0.0,19.0.0,19.0.1,19.1.0,20.0.0,21.0.0,21.0.2,21.0.3,22.0.0,22.1.0,22.1.1,22.2.0,22.2.1,23.0.0,23.0.1,23.1.0,23.1.1,23.2.0,23.2.1,23.3.0,23.4.0,24.0.0-alpha1,24.0.0-alpha2,24.0.0-beta1,24.0.0,24.1.0,24.1.1,24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <instantvideo versions="26.0.0-alpha1"/>
  <support-v4 versions="13.0.0,18.0.0,19.0.0,19.0.1,19.1.0,20.0.0,21.0.0,21.0.2,21.0.3,22.0.0,22.1.0,22.1.1,22.2.0,22.2.1,23.0.0,23.0.1,23.1.0,23.1.1,23.2.0,23.2.1,23.3.0,23.4.0,24.0.0-alpha1,24.0.0-alpha2,24.0.0-beta1,24.0.0,24.1.0,24.1.1,24.2.0,24.2.1,25.0.0,25.0.1,25.1.0,25.1.1,25.2.0,25.3.0,25.3.1,25.4.0,26.0.0-alpha1,26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <support-emoji versions="26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <wear versions="26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <support-emoji-appcompat versions="26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <support-emoji-bundled versions="26.0.0-beta1,26.0.0-beta2,26.0.0,26.0.1,26.0.2,26.1.0,27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <support-content versions="27.0.0,27.0.1,27.0.2,27.1.0,27.1.1,28.0.0-alpha1"/>
  <design-bottomnavigation versions="28.0.0-alpha1"/>
  <design-button versions="28.0.0-alpha1"/>
  <design-circularreveal-cardview versions="28.0.0-alpha1"/>
  <design-bottomappbar versions="28.0.0-alpha1"/>
  <design-card versions="28.0.0-alpha1"/>
  <design-shape versions="28.0.0-alpha1"/>
  <design-drawable versions="28.0.0-alpha1"/>
  <design-bottomsheet versions="28.0.0-alpha1"/>
  <design-floatingactionbutton versions="28.0.0-alpha1"/>
  <design-circularreveal-coordinatorlayout versions="28.0.0-alpha1"/>
  <design-textfield versions="28.0.0-alpha1"/>
  <design-stateful versions="28.0.0-alpha1"/>
  <design-circularreveal versions="28.0.0-alpha1"/>
  <design-expandable versions="28.0.0-alpha1"/>
  <design-navigation versions="28.0.0-alpha1"/>
  <design-dialog versions="28.0.0-alpha1"/>
  <design-canvas versions="28.0.0-alpha1"/>
  <design-tabs versions="28.0.0-alpha1"/>
  <design-chip versions="28.0.0-alpha1"/>
  <design-snackbar versions="28.0.0-alpha1"/>
  <design-theme versions="28.0.0-alpha1"/>
  <design-math versions="28.0.0-alpha1"/>
  <design-transformation versions="28.0.0-alpha1"/>
  <design-widget versions="28.0.0-alpha1"/>
  <design-animation versions="28.0.0-alpha1"/>
  <design-typography versions="28.0.0-alpha1"/>
  <design-color versions="28.0.0-alpha1"/>
  <design-internal versions="28.0.0-alpha1"/>
  <design-resources versions="28.0.0-alpha1"/>
  <design-ripple versions="28.0.0-alpha1"/>
  <coordinatorlayout versions="28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <collections versions="28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <slidingpanelayout versions="28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <asynclayoutinflater versions="28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <slices-view versions="28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <recyclerview-selection versions="28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <viewpager versions="28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <cursoradapter versions="28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <localbroadcastmanager versions="28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <heifwriter versions="28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <customview versions="28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <print versions="28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <slices-builders versions="28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <interpolator versions="28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <slices-core versions="28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <loader versions="28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <swiperefreshlayout versions="28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <drawerlayout versions="28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <documentfile versions="28.0.0-alpha1,28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <webkit versions="28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <car versions="28.0.0-alpha3,28.0.0-alpha4,28.0.0-alpha5"/>
  <versionedparcelable versions="28.0.0-alpha3,28.0.0-beta01,28.0.0-rc01,28.0.0-rc02,28.0.0"/>
  <media2 versions="28.0.0-alpha01,28.0.0-alpha02,28.0.0-alpha03"/>
</com.android.support>
