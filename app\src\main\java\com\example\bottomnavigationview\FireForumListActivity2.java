package com.example.bottomnavigationview;

import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Handler;
import android.os.Message;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v7.app.AppCompatActivity;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

// Firebase imports temporarily disabled
// import com.google.firebase.database.ChildEventListener;
// import com.google.firebase.database.DataSnapshot;
// import com.google.firebase.database.DatabaseError;
// import com.google.firebase.database.DatabaseReference;
// import com.google.firebase.database.FirebaseDatabase;
// import com.google.firebase.database.Query;
// import com.google.firebase.database.ValueEventListener;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.TimeZone;

// import static com.google.protobuf.WireFormat.FieldType.GROUP; // Temporarily disabled


//~~~~~~~~~~~~論壇清單~~~~~~~~~~~~~~~(登入後第一層)

public class FireForumListActivity2 extends AppCompatActivity {

    static final String DB_NAME = "HotlineDB";
    static final String TB_NAME = "hotlist";
    SQLiteDatabase db;
    Cursor cur;
    String[] kind;
    int rows_num;
    // DatabaseReference rootRef; // Temporarily disabled
    ListView fourmList;
    MyCustomAdapter myCustomAdapter=null;
    final ArrayList<FireFourmAdapterItem2> listForumData = new ArrayList<FireFourmAdapterItem2>();
    String x;
    Button btrefresh;

    private static final int LIST_PETS = 1;



    private Handler handler = new Handler() {  //~~~~~~~~~建立Handler物件才能使用handleMessage(Message msg)方法，
        public void handleMessage(Message msg) {   //~~~~~~~~~~~~Handler 經紀人(Thread 裡是無法做任何有關介面的事，所以我們必須倚賴 Handler，可以把它想成介面與執行緒之間的經紀人)
            switch (msg.what) {
                case LIST_PETS: {
                    ArrayList<FireFourmAdapterItem2> listForumData2 = (ArrayList<FireFourmAdapterItem2>)msg.obj;  // ~~~將 backThread帶到主程序中，由Message取出ArrayList<FireFourmAdapterItem2> listForumData2集合 ("2")
                    refreshPetList(listForumData2);  //~~~~~~~~原本backThread 的集合為listForumData，帶入主程序後此處為listForumData2
                    break;
                }
            }
        }
    };

    private void refreshPetList( ArrayList<FireFourmAdapterItem2> listForumData2) {  //~~~~~~~~~~~~~~~~~~~~~~~自己寫的更新方法，帶入主程序的"listForumData2"集合
        myCustomAdapter.clear();

        Random rand=new Random();//============================================隨機青菜食譜
        int k=rand.nextInt(rows_num);

        x=kind[k];

        myCustomAdapter.addAll(listForumData2);  //集合帶到主程序後將"listForumData2"集合放入 Adapter




    }



    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.fireforulist2);

        fourmList = (ListView)findViewById(R.id.fourmList);
        btrefresh=(Button)findViewById(R.id.btrefresh);
        // Firebase temporarily disabled
        // rootRef = FirebaseDatabase.getInstance().getReference().child("forum");

        // Initialize with dummy data for testing
        ArrayList<FireFourmAdapterItem2> dummyData = new ArrayList<>();
        dummyData.add(new FireFourmAdapterItem2("蔬菜討論區", System.currentTimeMillis(), "系統管理員", null, "dummy1"));
        dummyData.add(new FireFourmAdapterItem2("食譜分享", System.currentTimeMillis() - 3600000, "料理達人", null, "dummy2"));

        myCustomAdapter = new MyCustomAdapter(this, dummyData);
        fourmList.setAdapter(myCustomAdapter);


        ///=====================================================================資料庫~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~=====================================
        db=openOrCreateDatabase(DB_NAME, Context.MODE_PRIVATE,null);
        String createTable="CREATE TABLE IF NOT EXISTS " + TB_NAME + "(_id INTEGER PRIMARY KEY AUTOINCREMENT, " + "buydate VARCHAR(32), " + "savedate VARCHAR(32), " +"kind VARCHAR(32), " +"finekind VARCHAR(32), " +"part VARCHAR(32), " +"serving VARCHAR(32), " +"site VARCHAR(32), " +"remarks VARCHAR(32)," +"image VARCHAR(32)) ";
        db.execSQL(createTable);
//static final String[] FROM=new String[] {"buydate","savedate","kind","findkindt","part","serving","remarks"};//   1購買日, 2存放日, 3種類, 4子類, 5部位, 6份數, 7位置, 8備註 9.圖片
        cur=db.rawQuery("SELECT * FROM  hotlist  GROUP BY  finekind HAVING count(*)>1",null);
        rows_num = cur.getCount();
         kind=new String[rows_num];
        if(rows_num != 0) {

            //將指標移至第一筆資料
            cur.moveToFirst();

            for(int i=0; i<rows_num; i++) {//===========================不同食材名稱 寫入陣列========================================
                 kind[i] =cur.getString(cur.getColumnIndex("finekind"));

                //將指標移至下一筆資料
                cur.moveToNext();
            }
        }
        Random rand=new Random();//============================================隨機青菜食譜
        int k=rand.nextInt(rows_num);
        x=kind[k];




        btrefresh.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Refresh functionality temporarily disabled
                // onResume();
                Toast.makeText(FireForumListActivity2.this, "重新整理功能暫時停用", Toast.LENGTH_SHORT).show();
            }
        });

    }

    @Override
    protected void onResume() {
        super.onResume();
        // updateList(); //~~~~~~~Resume後再執行一次update (Firebase temporarily disabled)
    }







    // Firebase Thread temporarily disabled
    /*
    class FirebaseThread extends Thread {
        private DataSnapshot dataSnapshot;
        public FirebaseThread(DataSnapshot dataSnapshot) {
            this.dataSnapshot = dataSnapshot;
        }

        @Override
        public void run() {         //執行緒中要做的事，在run()方法中寫出，透過Message將介面要做的事情傳遞至介面~~~~~~~~~~~~~~
            final ArrayList<FireFourmAdapterItem2> listForumData = new ArrayList<>();



            for(DataSnapshot childDataSnapshot: dataSnapshot.getChildren()){  //          ~~~for (DataSnapshot "child" : "parent".getChildren())~~~~~~~將以下Child擷取的項目循環?
                String subject = childDataSnapshot.child("subject").getValue().toString();
                String lastupdate = childDataSnapshot.child("lastUpdate").getValue().toString();
                String lastUpdateUserNickname = childDataSnapshot.child("lastUpdateUserNickname").getValue().toString();
                String imgUrl = childDataSnapshot.child("album_file").getValue().toString();

                Bitmap petImg = getImgBitmap(imgUrl);  //~~~~~~~~~~~~~~使用取得圖片方法，另外寫個getImgBitmap方法來取得Url中的圖片(~~圖片檔 petImg ~~~~~~~~~~~)


                String key = childDataSnapshot.getKey();
                listForumData.add(new FireFourmAdapterItem2(subject,Long.parseLong(lastupdate),lastUpdateUserNickname,petImg ,key));  //~~~~~~~~~~都放進 listForumData集合中
            }



            Message msg = new Message();  //~~~~~~~~~~~~~~~~~~~~利用Message方法將 listForumData集合 帶到主程序中
            msg.what = LIST_PETS;
            msg.obj = listForumData;//~~~~~~~~~~集合放入Message
            handler.sendMessage(msg);
        }
    }
    */


    private void updateList(){
        // Firebase functionality temporarily disabled
        /*
        ValueEventListener valueEventListener = new ValueEventListener() {
            @Override
            public void onDataChange(DataSnapshot dataSnapshot) {
                new FirebaseThread(dataSnapshot).start();
            }
            public void onCancelled(DatabaseError databaseError) { }
        };
        Query query = rootRef.child("subject").orderByChild("id").equalTo(x);
        query.addListenerForSingleValueEvent(valueEventListener);
        */
    }

    private Bitmap getImgBitmap(String imgUrl) {  //~~~~~~~~~~~~~~~取得圖片URI的方法獨立寫出來
        try {
            URL url = new URL(imgUrl);
            Bitmap bm = BitmapFactory.decodeStream(url.openConnection()  //~~~~~~~~~~~~~~~~~~~~  url網址openConnection()打開連結，然後.getInputStream()取得串流，再由BitmapFactory.decodeStream轉為Bitmap檔
                    .getInputStream());
            return bm;
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }



      class  MyCustomAdapter extends ArrayAdapter<FireFourmAdapterItem2> {
        Context context;

        public MyCustomAdapter(@NonNull Context context, ArrayList<FireFourmAdapterItem2> listForumData) {
            super(context,0, listForumData);
            this.context = context;

        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {  //~~~~~~~~~~~~~~~~此處為將資料讀出並放入XML中
            LayoutInflater mInflater = LayoutInflater.from(context);
            LinearLayout itemlayout = null;
            if (convertView == null) {
                itemlayout = (LinearLayout) mInflater.inflate(R.layout.forum_listitem2, null);  // 決定XML
            } else {
                itemlayout = (LinearLayout) convertView;
            }
            final FireFourmAdapterItem2 s = (FireFourmAdapterItem2)getItem(position);  //~~~~~~~~~FourmAdapterItem類別創建不同物件S ，是由( listForumDataAdapter集合)中取出position特定位置的資料
            TextView subject = (TextView)itemlayout.findViewById(R.id.subject);
            subject.setText(s.subject); //~~~~~~~~"s.subject"為S的成員變數
            TextView date = (TextView)itemlayout.findViewById(R.id.lastUpdate);
            ImageView img=(ImageView)itemlayout.findViewById(R.id.imageView2) ;
            img.setImageBitmap(s.petImg);


            //~~~~~~~~~~~~~~~~~時間設定
            Date lastUpdateddate = new Date(s.lastUpdateDate);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("MM/dd HH:mm");
            simpleDateFormat.setTimeZone(TimeZone.getDefault());//~~~~~~~~~取得默認時區~~~~~~~~~~~~~
            String dateString = simpleDateFormat.format(lastUpdateddate);
                    date.setText(dateString);
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            TextView lastSpeaker = (TextView)itemlayout.findViewById(R.id.lastUpdateUserNickname);
            lastSpeaker.setText(s.lastUpdateUserNickname);
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            itemlayout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Log.d("lickID",s.key);
                    Intent intent = new Intent();
                    intent.setClass(FireForumListActivity2.this,FireDiscActivity2.class);
                    intent.putExtra("discKey",s.key);
                    intent.putExtra("subject",s.subject);  //~~~~~~~~~~將此層的key及subject帶到下層activity去
                    startActivity(intent);
                }
            });
            return itemlayout;
        }


    }


}

